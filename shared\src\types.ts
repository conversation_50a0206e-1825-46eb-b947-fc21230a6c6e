// 支持的加密货币
export enum CryptoCurrency {
  BTC = 'BTC',
  ETH = 'ETH',
  SOL = 'SOL'
}

// 交易对
export type TradingPair = `${CryptoCurrency}/USDT`;

// 策略类型
export enum StrategyType {
  TREND_FOLLOWING = 'TREND_FOLLOWING',
  MEAN_REVERSION = 'MEAN_REVERSION'
}

// 时间框架
export enum TimeFrame {
  M15 = '15m',
  H1 = '1h',
  H4 = '4h',
  D1 = '1d'
}

// 信号类型
export enum SignalType {
  BUY = 'BUY',
  SELL = 'SELL',
  HOLD = 'HOLD'
}

// K线数据
export interface Kline {
  timestamp: number;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

// 技术指标数据
export interface TechnicalIndicators {
  ema20: number;
  ema50: number;
  ema200: number;
  rsi: number;
  atr: number;
  bollingerBands: {
    upper: number;
    middle: number;
    lower: number;
    bandwidth: number;
  };
}

// 交易信号
export interface TradingSignal {
  id: string;
  timestamp: number;
  asset: TradingPair;
  strategy: StrategyType;
  timeFrame: TimeFrame;
  signalType: SignalType;
  score: number;
  price: number;
  stopLoss?: number;
  takeProfit?: number;
  reasoning: string[];
  indicators: TechnicalIndicators;
}

// 手动分析请求
export interface AnalyzeRequest {
  asset: TradingPair;
  strategy: StrategyType;
}

// 手动分析响应
export interface AnalyzeResponse {
  signal: TradingSignal;
  recommendation: string;
  confidence: number;
}

// 回测请求
export interface BacktestRequest {
  asset: TradingPair;
  strategy: StrategyType;
  startDate: string;
  endDate: string;
  initialCapital: number;
}

// 回测交易记录
export interface BacktestTrade {
  id: string;
  timestamp: number;
  type: 'BUY' | 'SELL';
  price: number;
  quantity: number;
  pnl?: number;
  reason: string;
}

// 回测结果
export interface BacktestResult {
  id: string;
  request: BacktestRequest;
  trades: BacktestTrade[];
  performance: {
    totalReturn: number;
    totalReturnPercent: number;
    maxDrawdown: number;
    winRate: number;
    profitFactor: number;
    sharpeRatio: number;
    totalTrades: number;
    winningTrades: number;
    losingTrades: number;
  };
  equityCurve: Array<{
    timestamp: number;
    equity: number;
  }>;
  createdAt: number;
  completedAt?: number;
}

// 回测任务状态
export interface BacktestJob {
  id: string;
  status: 'PENDING' | 'RUNNING' | 'COMPLETED' | 'FAILED';
  progress: number;
  result?: BacktestResult;
  error?: string;
  createdAt: number;
  updatedAt: number;
}

// 系统配置
export interface SystemConfig {
  ntfy: {
    url: string;
    topic: string;
    enabled: boolean;
  };
  binance: {
    apiUrl: string;
    testnet: boolean;
  };
  monitoring: {
    enabled: boolean;
    interval: number; // 分钟
    assets: TradingPair[];
  };
}

// 网格交易相关类型
export interface GridTradingConfig {
  id: string;
  symbol: string;
  baseAmount: number;
  gridCount: number;
  priceRange: {
    min: number;
    max: number;
  };
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface GridTradingPosition {
  id: string;
  configId: string;
  symbol: string;
  side: 'buy' | 'sell';
  quantity: number;
  price: number;
  value: number;
  pnl: number;
  createdAt: Date;
}

export interface GridTradingOrder {
  id: string;
  configId: string;
  type: 'buy' | 'sell';
  price: number;
  quantity: number;
  status: 'pending' | 'filled' | 'cancelled';
  gridLevel: number;
  createdAt: Date;
}

export interface GridTradingPerformance {
  id: string;
  configId: string;
  totalPnl: number;
  totalVolume: number;
  winRate: number;
  maxDrawdown: number;
  timestamp: Date;
}

// 网格交易DTO
export interface CreateGridConfigDto {
  symbol: string;
  baseAmount: number;
  gridCount: number;
  priceRange: {
    min: number;
    max: number;
  };
}

export interface UpdateGridConfigDto {
  symbol?: string;
  baseAmount?: number;
  gridCount?: number;
  priceRange?: {
    min: number;
    max: number;
  };
  isActive?: boolean;
}
