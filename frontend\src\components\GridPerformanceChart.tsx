import { useState, useEffect, useRef } from 'react';
import { create<PERSON>hart, IChartApi, ISeriesApi, LineStyle } from 'lightweight-charts';
import { GridPerformance } from '../types/gridTrading';

interface GridPerformanceChartProps {
  data: GridPerformance;
}

export function GridPerformanceChart({ data }: GridPerformanceChartProps) {
  const chartContainerRef = useRef<HTMLDivElement>(null);
  const chartRef = useRef<IChartApi | null>(null);
  const profitSeriesRef = useRef<ISeriesApi<'Line'> | null>(null);
  const [selectedTab, setSelectedTab] = useState<'profit' | 'stats'>('profit');

  useEffect(() => {
    if (!chartContainerRef.current) return;

    // 创建图表
    const chart = createChart(chartContainerRef.current, {
      width: chartContainerRef.current.clientWidth,
      height: 400,
      layout: {
        background: { color: '#ffffff' },
        textColor: '#333',
      },
      grid: {
        vertLines: { color: '#f0f0f0' },
        horzLines: { color: '#f0f0f0' },
      },
      crosshair: {
        mode: 1,
      },
      rightPriceScale: {
        borderColor: '#cccccc',
      },
      timeScale: {
        borderColor: '#cccccc',
        timeVisible: true,
        secondsVisible: false,
      },
    });

    chartRef.current = chart;

    // 创建盈亏曲线
    const profitSeries = chart.addLineSeries({
      color: '#2563eb',
      lineWidth: 2,
      crosshairMarkerVisible: true,
      crosshairMarkerRadius: 4,
      title: '累计盈亏',
    });

    profitSeriesRef.current = profitSeries;

    // 处理数据
    const chartData = data.profitHistory.map(item => ({
      time: new Date(item.timestamp).getTime() / 1000 as any,
      value: item.cumulativeProfit,
    }));

    profitSeries.setData(chartData);

    // 添加零线
    const zeroLineSeries = chart.addLineSeries({
      color: '#6b7280',
      lineWidth: 1,
      lineStyle: LineStyle.Dashed,
      crosshairMarkerVisible: false,
      lastValueVisible: false,
      priceLineVisible: false,
    });

    const zeroLineData = chartData.map(item => ({
      time: item.time as any,
      value: 0,
    }));

    zeroLineSeries.setData(zeroLineData);

    // 响应式处理
    const handleResize = () => {
      if (chartContainerRef.current && chartRef.current) {
        chartRef.current.applyOptions({
          width: chartContainerRef.current.clientWidth,
        });
      }
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
      if (chartRef.current) {
        chartRef.current.remove();
      }
    };
  }, [data]);

  const formatCurrency = (value: number) => {
    return value.toLocaleString(undefined, { 
      style: 'currency', 
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    });
  };

  const formatPercentage = (value: number) => {
    return `${value.toFixed(2)}%`;
  };

  const getPerformanceColor = (value: number) => {
    return value >= 0 ? 'text-green-600' : 'text-red-600';
  };

  return (
    <div className="space-y-6">
      {/* 标签页 */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setSelectedTab('profit')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              selectedTab === 'profit'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            盈亏曲线
          </button>
          <button
            onClick={() => setSelectedTab('stats')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              selectedTab === 'stats'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            统计数据
          </button>
        </nav>
      </div>

      {selectedTab === 'profit' && (
        <div className="space-y-4">
          {/* 关键指标 */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="bg-gray-50 rounded-lg p-4">
              <p className="text-sm text-gray-600">净盈亏</p>
              <p className={`text-xl font-semibold ${getPerformanceColor(data.netProfit)}`}>
                {formatCurrency(data.netProfit)}
              </p>
            </div>
            <div className="bg-gray-50 rounded-lg p-4">
              <p className="text-sm text-gray-600">总交易数</p>
              <p className="text-xl font-semibold text-gray-900">{data.totalTrades}</p>
            </div>
            <div className="bg-gray-50 rounded-lg p-4">
              <p className="text-sm text-gray-600">胜率</p>
              <p className={`text-xl font-semibold ${getPerformanceColor(data.winRate - 50)}`}>
                {formatPercentage(data.winRate)}
              </p>
            </div>
            <div className="bg-gray-50 rounded-lg p-4">
              <p className="text-sm text-gray-600">夏普比率</p>
              <p className={`text-xl font-semibold ${getPerformanceColor(data.sharpeRatio)}`}>
                {data.sharpeRatio.toFixed(2)}
              </p>
            </div>
          </div>

          {/* 图表 */}
          <div className="bg-white border border-gray-200 rounded-lg p-4">
            <div ref={chartContainerRef} className="w-full" />
          </div>
        </div>
      )}

      {selectedTab === 'stats' && (
        <div className="space-y-6">
          {/* 交易统计 */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <h4 className="text-lg font-medium text-gray-900 mb-4">交易统计</h4>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">总交易数:</span>
                  <span className="text-sm font-medium text-gray-900">{data.totalTrades}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">盈利交易:</span>
                  <span className="text-sm font-medium text-green-600">{data.winningTrades}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">亏损交易:</span>
                  <span className="text-sm font-medium text-red-600">{data.losingTrades}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">胜率:</span>
                  <span className={`text-sm font-medium ${getPerformanceColor(data.winRate - 50)}`}>
                    {formatPercentage(data.winRate)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">盈亏比:</span>
                  <span className={`text-sm font-medium ${getPerformanceColor(data.profitFactor - 1)}`}>
                    {data.profitFactor.toFixed(2)}
                  </span>
                </div>
              </div>
            </div>

            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <h4 className="text-lg font-medium text-gray-900 mb-4">盈亏分析</h4>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">总盈利:</span>
                  <span className="text-sm font-medium text-green-600">{formatCurrency(data.totalProfit)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">总手续费:</span>
                  <span className="text-sm font-medium text-red-600">{formatCurrency(data.totalFees)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">净盈利:</span>
                  <span className={`text-sm font-medium ${getPerformanceColor(data.netProfit)}`}>
                    {formatCurrency(data.netProfit)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">最大回撤:</span>
                  <span className="text-sm font-medium text-red-600">{formatPercentage(data.maxDrawdown)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">夏普比率:</span>
                  <span className={`text-sm font-medium ${getPerformanceColor(data.sharpeRatio)}`}>
                    {data.sharpeRatio.toFixed(2)}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* 单笔交易统计 */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <h4 className="text-lg font-medium text-gray-900 mb-4">单笔交易</h4>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">平均盈利:</span>
                  <span className="text-sm font-medium text-green-600">{formatCurrency(data.averageProfit)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">平均亏损:</span>
                  <span className="text-sm font-medium text-red-600">{formatCurrency(Math.abs(data.averageLoss))}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">最大盈利:</span>
                  <span className="text-sm font-medium text-green-600">{formatCurrency(data.largestWin)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">最大亏损:</span>
                  <span className="text-sm font-medium text-red-600">{formatCurrency(Math.abs(data.largestLoss))}</span>
                </div>
              </div>
            </div>

            {/* 每日统计 */}
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <h4 className="text-lg font-medium text-gray-900 mb-4">近期表现</h4>
              <div className="space-y-2 max-h-48 overflow-y-auto">
                {data.dailyStats.slice(-7).map((stat, index) => (
                  <div key={index} className="flex justify-between items-center py-1">
                    <span className="text-sm text-gray-600">
                      {new Date(stat.date).toLocaleDateString()}
                    </span>
                    <div className="text-right">
                      <div className={`text-sm font-medium ${getPerformanceColor(stat.profit)}`}>
                        {formatCurrency(stat.profit)}
                      </div>
                      <div className="text-xs text-gray-500">
                        {stat.trades} 笔交易
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
