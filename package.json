{"name": "signal-dash-pro", "version": "1.0.0", "description": "Signal-Dash Pro - 本地化交易决策平台", "private": true, "workspaces": ["backend", "frontend", "shared"], "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && npm run start:dev", "dev:frontend": "cd frontend && npm run dev", "build": "npm run build:shared && npm run build:backend && npm run build:frontend", "build:shared": "cd shared && npm run build", "build:backend": "cd backend && npm run build", "build:frontend": "cd frontend && npm run build", "docker:build": "docker build -t signal-dash-pro .", "docker:run": "docker run -p 3000:3000 -p 3001:3001 signal-dash-pro"}, "devDependencies": {"concurrently": "^8.2.2", "typescript": "^5.3.3"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "dependencies": {"sqlite3": "^5.1.7"}}