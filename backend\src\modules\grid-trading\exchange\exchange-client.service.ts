import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as ccxt from 'ccxt';
import { Decimal } from 'decimal.js';
import { MarketData, BalanceInfo, ExchangeInfo, OrderExecutionResult } from '../interfaces/grid-trading.interface';
import { KlineData } from '../strategies/volatility-calculator.service';

export interface OrderParams {
  symbol: string;
  side: 'buy' | 'sell';
  type: 'market' | 'limit';
  amount: number;
  price?: number;
  params?: any;
}

@Injectable()
export class ExchangeClientService {
  private readonly logger = new Logger(ExchangeClientService.name);
  private exchange: ccxt.binance;
  private marketsLoaded = false;
  private timeDiff = 0;
  
  // 缓存
  private balanceCache: { timestamp: number; data: any } = { timestamp: 0, data: null };
  private marketDataCache = new Map<string, { timestamp: number; data: MarketData }>();
  private readonly cacheTimeout = 30000; // 30秒缓存

  constructor(private readonly configService: ConfigService) {
    this.initializeExchange();
  }

  private initializeExchange(): void {
    const apiKey = this.configService.get<string>('BINANCE_API_KEY');
    const apiSecret = this.configService.get<string>('BINANCE_API_SECRET');
    const proxy = this.configService.get<string>('HTTP_PROXY');

    if (!apiKey || !apiSecret) {
      throw new Error('Binance API credentials not configured');
    }

    this.exchange = new ccxt.binance({
      apiKey,
      secret: apiSecret,
      enableRateLimit: true,
      timeout: 60000,
      options: {
        defaultType: 'spot',
        adjustForTimeDifference: true,
        recvWindow: 5000,
      },
      ...(proxy && { aiohttp_proxy: proxy }),
    });

    this.logger.log('Exchange client initialized');
  }

  /**
   * 加载市场数据
   */
  async loadMarkets(): Promise<void> {
    if (this.marketsLoaded) return;

    try {
      await this.syncTime();
      await this.exchange.loadMarkets();
      this.marketsLoaded = true;
      this.logger.log('Markets loaded successfully');
    } catch (error) {
      this.logger.error(`Failed to load markets: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 同步服务器时间
   */
  async syncTime(): Promise<void> {
    try {
      const serverTime = await this.exchange.fetchTime();
      const localTime = Date.now();
      this.timeDiff = serverTime - localTime;
      this.logger.debug(`Time synchronized, diff: ${this.timeDiff}ms`);
    } catch (error) {
      this.logger.error(`Time sync failed: ${error.message}`);
    }
  }

  /**
   * 验证交易对
   */
  async validateSymbol(symbol: string): Promise<boolean> {
    try {
      await this.loadMarkets();
      return symbol in this.exchange.markets;
    } catch (error) {
      this.logger.error(`Symbol validation failed: ${error.message}`);
      return false;
    }
  }

  /**
   * 获取当前价格
   */
  async getCurrentPrice(symbol: string): Promise<number> {
    try {
      const ticker = await this.exchange.fetchTicker(symbol);
      return ticker.last || ticker.close;
    } catch (error) {
      this.logger.error(`Failed to get current price for ${symbol}: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取市场数据
   */
  async getMarketData(symbol: string): Promise<MarketData> {
    const cacheKey = symbol;
    const cached = this.marketDataCache.get(cacheKey);
    
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.data;
    }

    try {
      const ticker = await this.exchange.fetchTicker(symbol);
      const orderBook = await this.exchange.fetchOrderBook(symbol, 5);

      const marketData: MarketData = {
        symbol,
        price: ticker.last || ticker.close,
        volume: ticker.baseVolume || 0,
        timestamp: new Date(ticker.timestamp || Date.now()),
        bid: orderBook.bids[0]?.[0] || 0,
        ask: orderBook.asks[0]?.[0] || 0,
        spread: 0,
      };

      marketData.spread = marketData.ask - marketData.bid;

      // 更新缓存
      this.marketDataCache.set(cacheKey, {
        timestamp: Date.now(),
        data: marketData,
      });

      return marketData;
    } catch (error) {
      this.logger.error(`Failed to get market data for ${symbol}: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取K线数据
   */
  async getKlineData(
    symbol: string,
    timeframe: string = '4h',
    limit: number = 100,
  ): Promise<KlineData[]> {
    try {
      const ohlcv = await this.exchange.fetchOHLCV(symbol, timeframe, undefined, limit);
      
      return ohlcv.map(candle => ({
        timestamp: candle[0],
        open: candle[1],
        high: candle[2],
        low: candle[3],
        close: candle[4],
        volume: candle[5],
      }));
    } catch (error) {
      this.logger.error(`Failed to get kline data for ${symbol}: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取账户余额
   */
  async getBalance(): Promise<BalanceInfo[]> {
    const now = Date.now();
    if (this.balanceCache.data && now - this.balanceCache.timestamp < this.cacheTimeout) {
      return this.balanceCache.data;
    }

    try {
      const balance = await this.exchange.fetchBalance();
      const balanceInfo: BalanceInfo[] = [];

      for (const [asset, info] of Object.entries(balance)) {
        if (asset === 'info' || asset === 'free' || asset === 'used' || asset === 'total') {
          continue;
        }

        const assetInfo = info as any;
        if (assetInfo.total > 0) {
          balanceInfo.push({
            asset,
            free: assetInfo.free || 0,
            used: assetInfo.used || 0,
            total: assetInfo.total || 0,
          });
        }
      }

      // 更新缓存
      this.balanceCache = {
        timestamp: now,
        data: balanceInfo,
      };

      return balanceInfo;
    } catch (error) {
      this.logger.error(`Failed to get balance: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取交易对信息
   */
  async getExchangeInfo(symbol: string): Promise<ExchangeInfo> {
    try {
      await this.loadMarkets();
      const market = this.exchange.market(symbol);

      return {
        symbol,
        baseAsset: market.base,
        quoteAsset: market.quote,
        minQty: market.limits.amount?.min || 0,
        maxQty: market.limits.amount?.max || Number.MAX_SAFE_INTEGER,
        stepSize: market.precision.amount || 0.00000001,
        minPrice: market.limits.price?.min || 0,
        maxPrice: market.limits.price?.max || Number.MAX_SAFE_INTEGER,
        tickSize: market.precision.price || 0.01,
        minNotional: market.limits.cost?.min || 0,
      };
    } catch (error) {
      this.logger.error(`Failed to get exchange info for ${symbol}: ${error.message}`);
      throw error;
    }
  }

  /**
   * 创建订单
   */
  async createOrder(orderParams: OrderParams): Promise<OrderExecutionResult> {
    try {
      await this.syncTime();

      const params = {
        ...orderParams.params,
        timestamp: Date.now() + this.timeDiff,
        recvWindow: 5000,
      };

      this.logger.log(`Creating ${orderParams.side} order for ${orderParams.symbol}: ${orderParams.amount} @ ${orderParams.price || 'market'}`);

      const order = await this.exchange.createOrder(
        orderParams.symbol,
        orderParams.type,
        orderParams.side,
        orderParams.amount,
        orderParams.price,
        params,
      );

      return {
        success: true,
        orderId: order.id,
        executedPrice: order.price || order.average,
        executedQuantity: order.filled || order.amount,
        fees: order.fee?.cost || 0,
      };
    } catch (error) {
      this.logger.error(`Order creation failed: ${error.message}`, error.stack);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * 取消订单
   */
  async cancelOrder(orderId: string, symbol: string): Promise<boolean> {
    try {
      await this.exchange.cancelOrder(orderId, symbol);
      this.logger.log(`Order cancelled: ${orderId}`);
      return true;
    } catch (error) {
      this.logger.error(`Failed to cancel order ${orderId}: ${error.message}`);
      return false;
    }
  }

  /**
   * 取消所有订单
   */
  async cancelAllOrders(symbol: string): Promise<number> {
    try {
      const openOrders = await this.exchange.fetchOpenOrders(symbol);
      let cancelledCount = 0;

      for (const order of openOrders) {
        try {
          await this.exchange.cancelOrder(order.id, symbol);
          cancelledCount++;
        } catch (error) {
          this.logger.error(`Failed to cancel order ${order.id}: ${error.message}`);
        }
      }

      this.logger.log(`Cancelled ${cancelledCount} orders for ${symbol}`);
      return cancelledCount;
    } catch (error) {
      this.logger.error(`Failed to cancel all orders for ${symbol}: ${error.message}`);
      return 0;
    }
  }

  /**
   * 获取订单状态
   */
  async getOrderStatus(orderId: string, symbol: string): Promise<any> {
    try {
      return await this.exchange.fetchOrder(orderId, symbol);
    } catch (error) {
      this.logger.error(`Failed to get order status ${orderId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取交易历史
   */
  async getTradeHistory(symbol: string, limit: number = 100): Promise<any[]> {
    try {
      return await this.exchange.fetchMyTrades(symbol, undefined, limit);
    } catch (error) {
      this.logger.error(`Failed to get trade history for ${symbol}: ${error.message}`);
      throw error;
    }
  }

  /**
   * 精度调整
   */
  adjustPrecision(value: number, precision: number): number {
    return parseFloat(value.toFixed(precision));
  }

  /**
   * 计算订单数量（考虑精度）
   */
  async calculateOrderAmount(
    symbol: string,
    targetValue: number,
    price: number,
  ): Promise<number> {
    const exchangeInfo = await this.getExchangeInfo(symbol);
    const rawAmount = targetValue / price;
    
    // 应用最小数量限制
    const amount = Math.max(rawAmount, exchangeInfo.minQty);
    
    // 调整精度
    const stepSize = exchangeInfo.stepSize;
    const precision = stepSize.toString().split('.')[1]?.length || 0;
    
    return this.adjustPrecision(amount, precision);
  }

  /**
   * 关闭连接
   */
  async close(): Promise<void> {
    try {
      await this.exchange.close();
      this.logger.log('Exchange connection closed');
    } catch (error) {
      this.logger.error(`Failed to close exchange connection: ${error.message}`);
    }
  }
}
