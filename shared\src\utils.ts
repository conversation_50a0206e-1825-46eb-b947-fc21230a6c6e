import { TradingPair, CryptoCurrency } from './types.js';
import { BINANCE_SYMBOL_MAP } from './constants.js';

/**
 * 将交易对转换为币安符号
 */
export function toBinanceSymbol(pair: TradingPair): string {
  return BINANCE_SYMBOL_MAP[pair];
}

/**
 * 从币安符号转换为交易对
 */
export function fromBinanceSymbol(symbol: string): TradingPair | null {
  const entry = Object.entries(BINANCE_SYMBOL_MAP).find(([_, binanceSymbol]) => binanceSymbol === symbol);
  return entry ? entry[0] as TradingPair : null;
}

/**
 * 生成唯一ID
 */
export function generateId(): string {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

/**
 * 格式化价格
 */
export function formatPrice(price: number, decimals: number = 4): string {
  return price.toFixed(decimals);
}

/**
 * 格式化百分比
 */
export function formatPercent(value: number, decimals: number = 2): string {
  return `${(value * 100).toFixed(decimals)}%`;
}

/**
 * 计算百分比变化
 */
export function calculatePercentChange(oldValue: number, newValue: number): number {
  if (oldValue === 0) return 0;
  return (newValue - oldValue) / oldValue;
}

/**
 * 验证交易对是否支持
 */
export function isValidTradingPair(pair: string): pair is TradingPair {
  return Object.keys(BINANCE_SYMBOL_MAP).includes(pair);
}

/**
 * 获取加密货币基础资产
 */
export function getBaseAsset(pair: TradingPair): CryptoCurrency {
  return pair.split('/')[0] as CryptoCurrency;
}

/**
 * 延迟函数
 */
export function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * 重试函数
 */
export async function retry<T>(
  fn: () => Promise<T>,
  maxAttempts: number = 3,
  delayMs: number = 1000
): Promise<T> {
  let lastError: Error;
  
  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error as Error;
      if (attempt < maxAttempts) {
        await delay(delayMs * attempt);
      }
    }
  }
  
  throw lastError!;
}
