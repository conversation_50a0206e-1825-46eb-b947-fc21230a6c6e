#!/bin/bash

# Signal-Dash Pro 数据备份脚本

set -e

# 配置
BACKUP_DIR="backups"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_NAME="signal-dash-backup-$DATE"

echo "💾 开始备份 Signal-Dash Pro 数据..."

# 创建备份目录
mkdir -p $BACKUP_DIR

# 创建临时备份目录
TEMP_BACKUP="$BACKUP_DIR/$BACKUP_NAME"
mkdir -p $TEMP_BACKUP

echo "📁 备份目录: $TEMP_BACKUP"

# 备份数据库
if [ -f "data/signal-dash.db" ]; then
    echo "📊 备份数据库..."
    cp data/signal-dash.db $TEMP_BACKUP/
    echo "✅ 数据库备份完成"
else
    echo "⚠️  数据库文件不存在"
fi

# 备份配置文件
echo "⚙️  备份配置文件..."
cp .env $TEMP_BACKUP/ 2>/dev/null || echo "⚠️  .env文件不存在"
cp docker-compose.yml $TEMP_BACKUP/ 2>/dev/null || echo "⚠️  docker-compose.yml文件不存在"

# 备份日志文件（最近7天）
if [ -d "logs" ]; then
    echo "📝 备份日志文件..."
    mkdir -p $TEMP_BACKUP/logs
    find logs -name "*.log" -mtime -7 -exec cp {} $TEMP_BACKUP/logs/ \; 2>/dev/null || true
    echo "✅ 日志备份完成"
fi

# 导出Redis数据
echo "🔴 备份Redis数据..."
if docker-compose ps redis | grep -q "Up"; then
    docker-compose exec -T redis redis-cli BGSAVE
    sleep 2
    docker cp $(docker-compose ps -q redis):/data/dump.rdb $TEMP_BACKUP/redis-dump.rdb 2>/dev/null || echo "⚠️  Redis备份失败"
    echo "✅ Redis备份完成"
else
    echo "⚠️  Redis服务未运行"
fi

# 创建备份信息文件
echo "📋 创建备份信息..."
cat > $TEMP_BACKUP/backup-info.txt << EOF
Signal-Dash Pro 备份信息
========================
备份时间: $(date)
备份版本: 1.0.0
系统信息: $(uname -a)

包含文件:
- signal-dash.db (SQLite数据库)
- .env (环境配置)
- docker-compose.yml (Docker配置)
- logs/ (日志文件)
- redis-dump.rdb (Redis数据)

恢复说明:
1. 停止当前服务: docker-compose down
2. 恢复数据库: cp signal-dash.db ../data/
3. 恢复配置: cp .env ../
4. 恢复Redis: docker cp redis-dump.rdb container:/data/dump.rdb
5. 重启服务: docker-compose up -d
EOF

# 压缩备份
echo "🗜️  压缩备份文件..."
cd $BACKUP_DIR
tar -czf $BACKUP_NAME.tar.gz $BACKUP_NAME
rm -rf $BACKUP_NAME
cd ..

# 显示备份结果
BACKUP_SIZE=$(du -h $BACKUP_DIR/$BACKUP_NAME.tar.gz | cut -f1)
echo ""
echo "✅ 备份完成！"
echo "📦 备份文件: $BACKUP_DIR/$BACKUP_NAME.tar.gz"
echo "📏 文件大小: $BACKUP_SIZE"

# 清理旧备份（保留最近10个）
echo "🧹 清理旧备份..."
cd $BACKUP_DIR
ls -t signal-dash-backup-*.tar.gz | tail -n +11 | xargs rm -f 2>/dev/null || true
cd ..

echo "🎉 备份任务完成！"

# 显示所有备份
echo ""
echo "📚 现有备份:"
ls -lh $BACKUP_DIR/signal-dash-backup-*.tar.gz 2>/dev/null || echo "暂无备份文件"
