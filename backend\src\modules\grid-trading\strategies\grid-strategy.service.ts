import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Decimal } from 'decimal.js';
import { GridTradingConfig } from '../entities/grid-trading-config.entity';
import { TradingPosition } from '../entities/trading-position.entity';
import { VolatilityCalculatorService, KlineData } from './volatility-calculator.service';
import { GridStatus, GridLevel, GridAdjustmentParams } from '../interfaces/grid-trading.interface';

export interface GridSignal {
  type: 'buy' | 'sell' | 'hold';
  price: number;
  quantity: number;
  gridLevel: number;
  confidence: number;
  reason: string;
}

export interface PriceAnalysis {
  currentPrice: number;
  pricePercentile: number;
  trendDirection: 'bullish' | 'bearish' | 'neutral';
  volatility: number;
  gridAdjustmentNeeded: boolean;
}

@Injectable()
export class GridStrategyService {
  private readonly logger = new Logger(GridStrategyService.name);

  // 网格状态缓存
  private gridStates = new Map<string, {
    basePrice: number;
    gridSize: number;
    upperBand: number;
    lowerBand: number;
    lastAdjustment: Date;
    isMonitoringBuy: boolean;
    isMonitoringSell: boolean;
    highest: number | null;
    lowest: number | null;
  }>();

  constructor(
    @InjectRepository(GridTradingConfig)
    private readonly configRepository: Repository<GridTradingConfig>,
    @InjectRepository(TradingPosition)
    private readonly positionRepository: Repository<TradingPosition>,
    private readonly volatilityCalculator: VolatilityCalculatorService,
  ) {}

  /**
   * 分析市场并生成交易信号
   */
  async analyzeMarket(
    symbol: string,
    klines: KlineData[],
    config: GridTradingConfig,
  ): Promise<GridSignal> {
    try {
      const currentPrice = klines[klines.length - 1].close;
      
      // 1. 获取或初始化网格状态
      const gridState = await this.getOrInitializeGridState(symbol, config, currentPrice);

      // 2. 检查是否需要调整网格
      if (config.enableVolatilityAdjustment) {
        await this.checkAndAdjustGrid(symbol, klines, config, gridState);
      }

      // 3. 分析价格位置
      const priceAnalysis = await this.analyzePricePosition(symbol, klines, gridState);

      // 4. 检查买入信号
      const buySignal = await this.checkBuySignal(gridState, priceAnalysis, config);
      if (buySignal.type === 'buy') {
        return buySignal;
      }

      // 5. 检查卖出信号
      const sellSignal = await this.checkSellSignal(gridState, priceAnalysis, config);
      if (sellSignal.type === 'sell') {
        return sellSignal;
      }

      // 6. 无交易信号
      return {
        type: 'hold',
        price: currentPrice,
        quantity: 0,
        gridLevel: 0,
        confidence: 0,
        reason: '价格在网格范围内，无交易信号',
      };

    } catch (error) {
      this.logger.error(`市场分析失败 ${symbol}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 检查买入信号
   */
  private async checkBuySignal(
    gridState: any,
    priceAnalysis: PriceAnalysis,
    config: GridTradingConfig,
  ): Promise<GridSignal> {
    const { currentPrice } = priceAnalysis;
    const { lowerBand, basePrice, gridSize } = gridState;

    // 检查是否触及下轨
    if (currentPrice <= lowerBand) {
      gridState.isMonitoringBuy = true;
      
      // 更新最低价
      if (gridState.lowest === null || currentPrice < gridState.lowest) {
        gridState.lowest = currentPrice;
      }

      // 计算反弹阈值（网格大小的1/5）
      const flipThreshold = (gridSize / 5) / 100;
      
      // 检查是否反弹
      if (gridState.lowest && currentPrice >= gridState.lowest * (1 + flipThreshold)) {
        // 触发买入
        gridState.isMonitoringBuy = false;
        gridState.highest = null;
        gridState.lowest = null;

        const quantity = await this.calculateOrderQuantity('buy', currentPrice, config);
        const gridLevel = this.calculateGridLevel(currentPrice, basePrice, gridSize);

        return {
          type: 'buy',
          price: currentPrice,
          quantity,
          gridLevel,
          confidence: 0.8,
          reason: `价格从 ${gridState.lowest?.toFixed(4)} 反弹至 ${currentPrice.toFixed(4)}，触发买入信号`,
        };
      }
    } else if (gridState.isMonitoringBuy) {
      // 价格回升，重置监控状态
      gridState.isMonitoringBuy = false;
      gridState.highest = null;
      gridState.lowest = null;
    }

    return {
      type: 'hold',
      price: currentPrice,
      quantity: 0,
      gridLevel: 0,
      confidence: 0,
      reason: '未触发买入条件',
    };
  }

  /**
   * 检查卖出信号
   */
  private async checkSellSignal(
    gridState: any,
    priceAnalysis: PriceAnalysis,
    config: GridTradingConfig,
  ): Promise<GridSignal> {
    const { currentPrice } = priceAnalysis;
    const { upperBand, basePrice, gridSize } = gridState;

    // 检查是否触及上轨
    if (currentPrice >= upperBand) {
      gridState.isMonitoringSell = true;
      
      // 更新最高价
      if (gridState.highest === null || currentPrice > gridState.highest) {
        gridState.highest = currentPrice;
      }

      // 计算回撤阈值（网格大小的1/5）
      const flipThreshold = (gridSize / 5) / 100;
      
      // 检查是否回撤
      if (gridState.highest && currentPrice <= gridState.highest * (1 - flipThreshold)) {
        // 触发卖出
        gridState.isMonitoringSell = false;
        gridState.highest = null;
        gridState.lowest = null;

        const quantity = await this.calculateOrderQuantity('sell', currentPrice, config);
        const gridLevel = this.calculateGridLevel(currentPrice, basePrice, gridSize);

        return {
          type: 'sell',
          price: currentPrice,
          quantity,
          gridLevel,
          confidence: 0.8,
          reason: `价格从 ${gridState.highest?.toFixed(4)} 回撤至 ${currentPrice.toFixed(4)}，触发卖出信号`,
        };
      }
    } else if (gridState.isMonitoringSell) {
      // 价格回落，重置监控状态
      gridState.isMonitoringSell = false;
      gridState.highest = null;
      gridState.lowest = null;
    }

    return {
      type: 'hold',
      price: currentPrice,
      quantity: 0,
      gridLevel: 0,
      confidence: 0,
      reason: '未触发卖出条件',
    };
  }

  /**
   * 分析价格位置
   */
  private async analyzePricePosition(
    symbol: string,
    klines: KlineData[],
    gridState: any,
  ): Promise<PriceAnalysis> {
    const currentPrice = klines[klines.length - 1].close;
    
    // 计算价格分位
    const pricePercentile = this.calculatePricePercentile(klines, currentPrice);
    
    // 判断趋势方向
    const trendDirection = this.analyzeTrend(klines);
    
    // 计算波动率
    const volatilityMetrics = await this.volatilityCalculator.calculateVolatilityMetrics(
      symbol,
      klines,
    );
    
    // 检查是否需要网格调整
    const gridAdjustmentNeeded = this.shouldAdjustGrid(
      volatilityMetrics.annualizedVolatility,
      gridState.gridSize,
      gridState.lastAdjustment,
    );

    return {
      currentPrice,
      pricePercentile,
      trendDirection,
      volatility: volatilityMetrics.annualizedVolatility,
      gridAdjustmentNeeded,
    };
  }

  /**
   * 计算价格分位
   */
  private calculatePricePercentile(klines: KlineData[], currentPrice: number): number {
    if (klines.length < 10) return 0.5;

    const prices = klines.map(k => k.close).sort((a, b) => a - b);
    const lowerQuartile = prices[Math.floor(prices.length * 0.25)];
    const upperQuartile = prices[Math.floor(prices.length * 0.75)];

    if (currentPrice <= lowerQuartile) return 0.0;
    if (currentPrice >= upperQuartile) return 1.0;
    
    return (currentPrice - lowerQuartile) / (upperQuartile - lowerQuartile);
  }

  /**
   * 分析趋势方向
   */
  private analyzeTrend(klines: KlineData[]): 'bullish' | 'bearish' | 'neutral' {
    if (klines.length < 20) return 'neutral';

    const prices = klines.map(k => k.close);
    const shortMA = this.calculateMA(prices.slice(-10));
    const longMA = this.calculateMA(prices.slice(-20));

    const diff = (shortMA - longMA) / longMA;
    
    if (diff > 0.02) return 'bullish';
    if (diff < -0.02) return 'bearish';
    return 'neutral';
  }

  /**
   * 计算移动平均
   */
  private calculateMA(prices: number[]): number {
    return prices.reduce((sum, price) => sum + price, 0) / prices.length;
  }

  /**
   * 检查并调整网格
   */
  private async checkAndAdjustGrid(
    symbol: string,
    klines: KlineData[],
    config: GridTradingConfig,
    gridState: any,
  ): Promise<void> {
    const now = new Date();
    const timeSinceLastAdjustment = now.getTime() - gridState.lastAdjustment.getTime();
    
    // 检查调整间隔
    if (timeSinceLastAdjustment < config.adjustmentInterval * 1000) {
      return;
    }

    // 计算当前波动率
    const volatilityMetrics = await this.volatilityCalculator.calculateVolatilityMetrics(
      symbol,
      klines,
      {
        window: config.volatilityWindow,
        ewmaLambda: config.ewmaLambda,
        hybridWeight: config.hybridWeight,
      },
    );

    // 根据波动率调整网格大小
    const newGridSize = this.calculateOptimalGridSize(
      volatilityMetrics.annualizedVolatility,
      config,
    );

    if (Math.abs(newGridSize - gridState.gridSize) > 0.1) {
      // 需要调整网格
      await this.adjustGrid(symbol, newGridSize, gridState, {
        newGridSize,
        reason: `波动率变化: ${volatilityMetrics.annualizedVolatility.toFixed(4)}`,
        volatility: volatilityMetrics.annualizedVolatility,
        priceChange: 0,
        timestamp: now,
      });
    }
  }

  /**
   * 计算最优网格大小
   */
  private calculateOptimalGridSize(volatility: number, config: GridTradingConfig): number {
    // 波动率到网格大小的映射规则
    const volatilityRanges = [
      { range: [0, 0.10], grid: 1.0 },
      { range: [0.10, 0.20], grid: 2.0 },
      { range: [0.20, 0.30], grid: 3.0 },
      { range: [0.30, 0.40], grid: 4.0 },
      { range: [0.40, 999], grid: 4.0 },
    ];

    for (const rule of volatilityRanges) {
      if (volatility >= rule.range[0] && volatility < rule.range[1]) {
        return Math.max(config.minGridSize, Math.min(rule.grid, config.maxGridSize));
      }
    }

    return config.gridSize; // 默认值
  }

  /**
   * 调整网格
   */
  private async adjustGrid(
    symbol: string,
    newGridSize: number,
    gridState: any,
    params: GridAdjustmentParams,
  ): Promise<void> {
    this.logger.log(`调整 ${symbol} 网格大小: ${gridState.gridSize}% -> ${newGridSize}%`);

    // 更新网格状态
    gridState.gridSize = newGridSize;
    gridState.upperBand = gridState.basePrice * (1 + newGridSize / 100);
    gridState.lowerBand = gridState.basePrice * (1 - newGridSize / 100);
    gridState.lastAdjustment = new Date();

    // 重置监控状态
    gridState.isMonitoringBuy = false;
    gridState.isMonitoringSell = false;
    gridState.highest = null;
    gridState.lowest = null;

    // 更新数据库配置
    await this.configRepository.update(
      { symbol },
      { 
        gridSize: newGridSize,
        updatedAt: new Date(),
      },
    );
  }

  /**
   * 计算订单数量
   */
  private async calculateOrderQuantity(
    side: 'buy' | 'sell',
    price: number,
    config: GridTradingConfig,
  ): Promise<number> {
    // 简化版本：固定使用初始资金的10%
    const targetValue = config.initialCapital * 0.1;
    return targetValue / price;
  }

  /**
   * 计算网格级别
   */
  private calculateGridLevel(currentPrice: number, basePrice: number, gridSize: number): number {
    const priceRatio = currentPrice / basePrice;
    const gridStep = gridSize / 100;
    return Math.round(Math.log(priceRatio) / Math.log(1 + gridStep));
  }

  /**
   * 获取网格状态
   */
  async getGridStatus(symbol: string): Promise<GridStatus> {
    const gridState = this.gridStates.get(symbol);
    
    if (!gridState) {
      throw new Error(`未找到 ${symbol} 的网格状态`);
    }

    return {
      upperBand: gridState.upperBand,
      lowerBand: gridState.lowerBand,
      currentLevel: 0, // TODO: 计算当前级别
      totalLevels: 10, // TODO: 计算总级别数
      activeBuyOrders: 0, // TODO: 查询活跃买单数
      activeSellOrders: 0, // TODO: 查询活跃卖单数
      nextBuyPrice: gridState.lowerBand,
      nextSellPrice: gridState.upperBand,
    };
  }

  /**
   * 获取或初始化网格状态
   */
  private async getOrInitializeGridState(
    symbol: string,
    config: GridTradingConfig,
    currentPrice: number,
  ): Promise<any> {
    let gridState = this.gridStates.get(symbol);

    if (!gridState) {
      const basePrice = config.basePrice || currentPrice;
      gridState = {
        basePrice,
        gridSize: config.gridSize,
        upperBand: basePrice * (1 + config.gridSize / 100),
        lowerBand: basePrice * (1 - config.gridSize / 100),
        lastAdjustment: new Date(),
        isMonitoringBuy: false,
        isMonitoringSell: false,
        highest: null,
        lowest: null,
      };
      this.gridStates.set(symbol, gridState);
    }

    return gridState;
  }

  /**
   * 判断是否应该调整网格
   */
  private shouldAdjustGrid(
    currentVolatility: number,
    currentGridSize: number,
    lastAdjustment: Date,
  ): boolean {
    const timeSinceAdjustment = Date.now() - lastAdjustment.getTime();
    const minInterval = 3600 * 1000; // 1小时

    if (timeSinceAdjustment < minInterval) {
      return false;
    }

    // 根据波动率判断是否需要调整
    const optimalGridSize = this.calculateOptimalGridSize(currentVolatility, {
      minGridSize: 1.0,
      maxGridSize: 4.0,
    } as GridTradingConfig);

    return Math.abs(optimalGridSize - currentGridSize) > 0.2;
  }
}
