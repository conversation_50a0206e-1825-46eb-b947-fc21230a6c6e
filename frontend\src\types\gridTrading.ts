export interface GridTradingConfig {
  id: string;
  symbol: string;
  basePrice?: number;
  gridSize: number;
  minGridSize: number;
  maxGridSize: number;
  initialCapital: number;
  minTradeAmount: number;
  maxPositionRatio: number;
  minPositionRatio: number;
  enableVolatilityAdjustment: boolean;
  volatilityWindow: number;
  ewmaLambda: number;
  hybridWeight: number;
  adjustmentInterval: number;
  enableRiskManagement: boolean;
  maxRetries: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface StartGridTradingDto {
  symbol: string;
  initialCapital: number;
  gridSize?: number;
  enableVolatilityAdjustment?: boolean;
  enableRiskManagement?: boolean;
}

export interface UpdateGridConfigDto {
  gridSize?: number;
  maxPositionRatio?: number;
  enableVolatilityAdjustment?: boolean;
  enableRiskManagement?: boolean;
  volatilityWindow?: number;
  ewmaLambda?: number;
  hybridWeight?: number;
}

export interface GridTradingSession {
  sessionId: string;
  symbol: string;
  isActive: boolean;
  startTime: string;
  lastActivity?: string;
  config: GridTradingConfig;
}

export interface GridStatus {
  upperBand: number;
  lowerBand: number;
  currentLevel: number;
  totalLevels: number;
  activeBuyOrders: number;
  activeSellOrders: number;
  nextBuyPrice: number;
  nextSellPrice: number;
}

export interface TradingPosition {
  id: string;
  symbol: string;
  sessionId: string;
  side: 'LONG' | 'SHORT';
  status: 'OPEN' | 'CLOSED' | 'CANCELLED';
  entryPrice: number;
  exitPrice?: number;
  quantity: number;
  remainingQuantity: number;
  totalValue: number;
  realizedPnl: number;
  fees: number;
  gridLevel: number;
  entryOrderId?: string;
  exitOrderId?: string;
  createdAt: string;
  closedAt?: string;
}

export interface OrderHistory {
  id: string;
  symbol: string;
  sessionId: string;
  exchangeOrderId: string;
  side: 'BUY' | 'SELL';
  type: 'MARKET' | 'LIMIT' | 'STOP_LOSS' | 'TAKE_PROFIT';
  status: 'PENDING' | 'OPEN' | 'PARTIALLY_FILLED' | 'FILLED' | 'CANCELLED' | 'REJECTED' | 'EXPIRED';
  price: number;
  quantity: number;
  filledQuantity: number;
  remainingQuantity: number;
  averagePrice: number;
  totalValue: number;
  fees: number;
  profit: number;
  gridLevel?: number;
  positionId?: string;
  errorMessage?: string;
  createdAt: string;
  filledAt?: string;
  cancelledAt?: string;
}

export interface RiskEvent {
  id: string;
  symbol: string;
  sessionId: string;
  eventType: 'POSITION_LIMIT_EXCEEDED' | 'CONSECUTIVE_FAILURES' | 'VOLATILITY_SPIKE' | 'BALANCE_INSUFFICIENT' | 'API_ERROR' | 'NETWORK_ERROR' | 'EMERGENCY_STOP' | 'MANUAL_INTERVENTION';
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  title: string;
  description?: string;
  triggerValue?: number;
  thresholdValue?: number;
  eventData?: any;
  actions?: string[];
  isResolved: boolean;
  resolvedAt?: string;
  resolvedBy?: string;
  createdAt: string;
}

export interface VolatilityData {
  id: string;
  symbol: string;
  timestamp: string;
  price: number;
  volume: number;
  logReturn: number;
  traditionalVolatility: number;
  ewmaVolatility: number;
  hybridVolatility: number;
  annualizedVolatility: number;
  volumeWeightedVolatility?: number;
  smoothedVolatility: number;
  windowSize: number;
  metadata?: any;
  createdAt: string;
}

export interface GridPerformance {
  symbol: string;
  sessionId: string;
  totalTrades: number;
  winningTrades: number;
  losingTrades: number;
  winRate: number;
  totalProfit: number;
  totalFees: number;
  netProfit: number;
  maxDrawdown: number;
  sharpeRatio: number;
  profitFactor: number;
  averageProfit: number;
  averageLoss: number;
  largestWin: number;
  largestLoss: number;
  profitHistory: Array<{
    timestamp: string;
    cumulativeProfit: number;
    tradeProfit: number;
  }>;
  dailyStats: Array<{
    date: string;
    trades: number;
    profit: number;
    fees: number;
  }>;
}

export interface PositionSummary {
  symbol: string;
  totalPositions: number;
  openPositions: number;
  closedPositions: number;
  totalValue: number;
  unrealizedPnl: number;
  realizedPnl: number;
  averageEntryPrice: number;
  currentPrice: number;
}

export interface RiskMetrics {
  currentPositionRatio: number;
  maxPositionRatio: number;
  consecutiveFailures: number;
  maxConsecutiveFailures: number;
  lastRiskCheck: string;
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  warnings: string[];
}

export interface MarketData {
  symbol: string;
  price: number;
  volume: number;
  timestamp: string;
  bid: number;
  ask: number;
  spread: number;
}

export interface BalanceInfo {
  asset: string;
  free: number;
  used: number;
  total: number;
}

export interface ExchangeInfo {
  symbol: string;
  baseAsset: string;
  quoteAsset: string;
  minQty: number;
  maxQty: number;
  stepSize: number;
  minPrice: number;
  maxPrice: number;
  tickSize: number;
  minNotional: number;
}

export interface OrderExecutionResult {
  success: boolean;
  orderId?: string;
  executedPrice?: number;
  executedQuantity?: number;
  fees?: number;
  error?: string;
}

export interface VolatilityMetrics {
  traditionalVolatility: number;
  ewmaVolatility: number;
  hybridVolatility: number;
  annualizedVolatility: number;
  volumeWeightedVolatility?: number;
  smoothedVolatility: number;
  timestamp: string;
}
