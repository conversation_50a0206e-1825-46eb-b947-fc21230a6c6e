import { Controller, Get, Post, Body, Patch } from '@nestjs/common';
import { MonitoringService, MonitoringResult } from './monitoring.service';

@Controller('monitoring')
export class MonitoringController {
  constructor(private readonly monitoringService: MonitoringService) {}

  /**
   * 获取当前监控配置
   */
  @Get('config')
  getMonitoringConfig() {
    return this.monitoringService.getMonitoringConfig();
  }

  /**
   * 更新监控配置
   */
  @Patch('config')
  updateMonitoringConfig(@Body() config: any) {
    this.monitoringService.updateMonitoringConfig(config);
    return { success: true, message: '监控配置已更新' };
  }

  /**
   * 手动触发监控
   */
  @Post('trigger')
  async triggerMonitoring(): Promise<{ results: MonitoringResult[] }> {
    const results = await this.monitoringService.triggerManualMonitoring();
    return { results };
  }

  /**
   * 获取监控状态
   */
  @Get('status')
  getMonitoringStatus() {
    const config = this.monitoringService.getMonitoringConfig();
    return {
      enabled: config.enabled,
      interval: config.interval,
      supportedPairs: config.supportedPairs,
      strategies: config.strategies,
      qualityThreshold: config.qualityThreshold,
      lastUpdate: new Date(),
    };
  }
}
