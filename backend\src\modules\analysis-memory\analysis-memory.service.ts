import { Injectable } from '@nestjs/common';

@Injectable()
export class AnalysisMemoryService {
  async analyzeSymbol(asset: string, strategy: string) {
    // 模拟分析结果
    const signalTypes = ['BUY', 'SELL', 'HOLD'];
    const signalType = signalTypes[Math.floor(Math.random() * signalTypes.length)];

    // 根据不同交易对生成合理价格
    let basePrice = 45000; // BTC/USDT 默认价格
    if (asset.includes('ETH')) {
      basePrice = 2500; // ETH/USDT
    } else if (asset.includes('SOL')) {
      basePrice = 150; // SOL/USDT
    }

    const price = basePrice + (Math.random() - 0.5) * basePrice * 0.1; // ±10% 波动
    const confidence = Math.random();
    const score = Math.floor(confidence * 100);

    return {
      signal: {
        signalType,
        price,
        score,
        stopLoss: signalType === 'BUY' ? price * 0.95 : signalType === 'SELL' ? price * 1.05 : null,
        takeProfit: signalType === 'BUY' ? price * 1.1 : signalType === 'SELL' ? price * 0.9 : null,
        reasoning: [
          `${strategy}策略显示${signalType === 'BUY' ? '买入' : signalType === 'SELL' ? '卖出' : '观望'}信号`,
          `RSI指标为${(Math.random() * 100).toFixed(1)}，${Math.random() > 0.5 ? '超买' : '超卖'}区域`,
          `MACD指标${Math.random() > 0.5 ? '金叉' : '死叉'}，趋势${Math.random() > 0.5 ? '向上' : '向下'}`,
          `布林带显示价格${Math.random() > 0.5 ? '接近上轨' : '接近下轨'}`,
        ],
      },
      confidence,
      timestamp: new Date(),
    };
  }

  async getAnalysisHistory() {
    // 模拟历史分析数据
    const history = [];
    for (let i = 0; i < 10; i++) {
      history.push({
        id: `analysis_${i}`,
        symbol: 'BTCUSDT',
        strategy: 'trend-following',
        signal: Math.random() > 0.5 ? 'BUY' : 'SELL',
        confidence: Math.random() * 100,
        price: 45000 + Math.random() * 10000,
        timestamp: new Date(Date.now() - i * 3600000),
      });
    }
    return history;
  }
}
