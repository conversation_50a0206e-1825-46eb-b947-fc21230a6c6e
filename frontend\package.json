{"name": "@signal-dash/frontend", "version": "1.0.0", "description": "Signal-Dash Pro Frontend", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0"}, "dependencies": {"@signal-dash/shared": "workspace:*", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "@tanstack/react-query": "^5.14.2", "axios": "^1.6.2", "socket.io-client": "^4.7.4", "lightweight-charts": "^4.1.3", "lucide-react": "^0.294.0", "clsx": "^2.0.0", "tailwind-merge": "^2.2.0", "date-fns": "^3.0.6"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "@tanstack/react-query-devtools": "^5.14.2", "autoprefixer": "^10.4.16", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "typescript": "^5.2.2", "vite": "^5.0.8"}}