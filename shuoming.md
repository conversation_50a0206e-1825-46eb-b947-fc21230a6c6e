好的，我们来构建一个功能全面升级的**终极版方案**。这次的**Signal-Dash Pro**将包含您要求的所有高级功能：一个用于即时决策的**“手动信号分析”**模块，以及一个用于策略验证的**“历史回测”**模块。这将使其从一个被动的信号提醒工具，进化为一个主动的、完整的交易决策和分析平台。

---

### **项目方案：Signal-Dash Pro (本地化交易决策平台)**

#### **1. 系统概述与核心定位**

**Signal-Dash Pro** 是一个运行在您本地计算机上的、一体化的交易决策平台。它为个人交易者提供了一个集**自动信号监控、手动即时分析、策略历史回测**于一体的专业级环境。

平台无需注册登录，所有配置、历史信号和回测结果均存储在本地，确保了数据的绝对隐私和系统的极致性能。其核心是围绕**两种截然不同的交易哲学（趋势跟踪与震荡区间）**构建的一套强大工具：

1.  **自动信号模式**: 在后台7x24小时为您监控市场，通过`ntfy`推送高确定性的交易机会。
2.  **手动分析模式**: 赋予您“上帝视角”，在任何您感兴趣的时刻，一键调用核心策略算法，对当前市场进行即时分析和评分，辅助您做出快速决策。
3.  **策略回测模式**: 提供一个可视化界面，让您可以在真实的历史数据上运行内置策略，量化其长期表现，帮助您建立对策略的信心或进行优化。

**核心定位**: 一个将**自动化发现**与**主动决策**相结合，并用**数据回测**验证的、闭环的个人交易工作站。

---

#### **2. 系统功能设计 (Functional Design)**

##### **模块 1: 后端服务 (Backend Service)**
*   **API增强**:
    *   **手动分析API (`POST /api/analyze-now`)**: 接收一个`{ asset, strategy }`的请求，立即从币安API拉取最新数据，执行相应的策略分析，并直接返回分析结果（信号类型、评分、SL/TP），而不保存或推送。
    *   **回测API (`POST /api/backtest`)**: 接收一个`{ asset, strategy, startDate, endDate, initialCapital }`的请求，启动一个耗时较长的后台回测任务。
    *   **回测进度API (`GET /api/backtest/:jobId/status`)**: 通过WebSocket或轮询，向前端报告回测进度。
    *   **回测结果API (`GET /api/backtest/:jobId/results`)**: 获取已完成的回测结果。
*   **数据持久化**:
    *   `config.json`: 存储系统配置。
    *   `signals.db` (SQLite): 存储由**自动信号模式**生成的所有信号。
    *   `backtests/` (目录): 每个回测任务完成后，将其详细结果（交易日志、绩效报告）保存为一个独立的JSON文件。

##### **模块 2: 前端用户界面 (Frontend UI)**

这是一个包含多个高级页面的单页面应用(SPA)。

*   **页面 1: 仪表盘 (Dashboard)**
    *   (与前一版类似) 实时展示**自动模式**产生的信号流、任务状态和核心统计数据。

*   **页面 2: 手动分析 (Analyze Now)**
    *   **界面布局**:
        *   顶部是输入区：一个选择框选择**交易对** (e.g., BTC/USDT)，一组按钮选择**策略模型** (趋势/震荡)。
        *   一个醒目的“**立即分析**”按钮。
    *   **结果展示区**:
        *   点击按钮后，这里会显示一个加载动画，然后展示后端返回的分析结果卡片。
        *   **卡片内容**:
            *   **明确结论**: “🟢 **建议买入**” / “🔴 **建议卖出**” / “⚪️ **建议观望**”。
            *   **信号评分**: 85/100。
            *   **风险建议**: 止损价、止盈价。
            *   **分析依据 (Details)**: 一个可展开的区域，用人类可读的语言列出评分的主要依据，例如：“趋势分析: 宏观趋势向上 (+20分)”、“K线形态: 出现强看涨吞没 (+20分)”、“成交量: 显著放大 (+15分)”。

*   **页面 3: 策略回测 (Backtester)**
    *   **配置区**:
        *   选择**交易对**和**策略模型**。
        *   使用日期选择器设置**回测开始日期**和**结束日期**。
        *   输入**初始模拟资金** (e.g., 10,000 USD)。
        *   “**开始回测**”按钮。
    *   **进度与结果区**:
        *   点击按钮后，显示一个进度条，实时更新回测进度。
        *   回测完成后，展示一份详细的**绩效报告**:
            *   **核心指标**: 净利润、总收益率、最大回撤、胜率、盈亏比。
            *   **图表**: 资金曲线图 (Equity Curve)。
            *   **交易列表**: 一个表格，详细列出回测期间的每一笔模拟交易（买入/卖出时间、价格、盈亏等）。

*   **页面 4: 系统配置 (Configuration)**
    *   (与前一版类似) 管理`ntfy`设置和**自动模式**的监控任务。

---

#### **3. 核心技术指标与策略逻辑详解**

##### **3.1 核心技术指标 (Technical Indicators)**

*   **移动平均线 (EMA - Exponential Moving Average)**:
    *   **用途**: 趋势判断的核心。EMA比SMA更重视近期价格，反应更灵敏。
    *   **参数**: `EMA(20)`用于短期动态支撑/阻力，`EMA(50)`和`EMA(200)`用于定义中长期趋势方向。
*   **布林带 (Bollinger Bands)**:
    *   **用途**: 识别震荡区间和市场波动性。
    *   **参数**: `SMA(20)`作为中轨，上下轨为中轨加减2倍的20周期标准差。带宽 `(Upper - Lower) / Middle` 是判断震荡状态的关键。
*   **相对强弱指数 (RSI - Relative Strength Index)**:
    *   **用途**: 判断市场的超买/超卖状态，是震荡策略的主要触发指标。
    *   **参数**: 周期 `14`。`>70`为超买，`<30`为超卖。
*   **平均真实波幅 (ATR - Average True Range)**:
    *   **用途**: 衡量市场波动率，**仅用于动态计算止损距离**，不作为方向判断指标。
    *   **参数**: 周期 `14`。一个更客观的、随市场变化的“尺子”。

##### **3.2 策略逻辑详解 (细化)**

*   **策略一: 趋势跟踪 (Trend Following / H4)**
    1.  **前提条件**: `EMA(50) > EMA(200)` (多头趋势) 或 `EMA(50) < EMA(200)` (空头趋势)。
    2.  **触发逻辑**:
        *   **多头**: 价格回调，`low`价触及或下穿`EMA(20)`，随后出现一根收盘价高于前一根K线开盘价的**看涨吞没K线**。
        *   **空头**: 价格反弹，`high`价触及或上穿`EMA(20)`，随后出现一根收盘价低于前一根K线开盘价的**看跌吞没K线**。
    3.  **风控**:
        *   **止损**: `买入`信号止损设置在吞没K线组合的最低点下方`0.25 * ATR(14)`处；`卖出`则在最高点上方。
        *   **止盈**: 无固定目标，建议“追踪止损”。

*   **策略二: 震荡区间 (Mean Reversion / M15)**
    1.  **前提条件**: 布林带带宽在过去50根K线内保持在`4%`以下，且中轨`SMA(20)`的斜率接近于零。
    2.  **触发逻辑**:
        *   **买入**: 价格`low`触及或跌破布林带下轨 **并且** `RSI(14) < 30`。
        *   **卖出**: 价格`high`触及或突破布林带上轨 **并且** `RSI(14) > 70`。
    3.  **风控**:
        *   **止损**: `买入`信号止损设置在入场K线低点下方`1 * ATR(14)`处；`卖出`则在K线高点上方。
        *   **止盈**: 布林带中轨`SMA(20)`。

---

#### **4. 技术架构与选型 (Technical Stack)**

*   **后端 (NestJS)**:
    *   **数据库**: **SQLite** (`typeorm` 或 `prisma`)。
    *   **API & WebSocket**: `@nestjs/platform-express`, `@nestjs/websockets`。
    *   **后台任务**: 使用**BullMQ (`@nestjs/bull`)**来管理**回测**这种长时间运行的作业。这比简单的`worker_threads`更健壮，能提供任务队列、进度跟踪和结果存储。
*   **前端 (React)**:
    *   **图表**: **Lightweight Charts™ by TradingView** 或 **ECharts**。这些库比Recharts更专业，能轻松绘制K线图和资金曲线。
    *   **UI**: Tailwind CSS, Shadcn UI。

---

#### **5. 最终版用户流程**

1.  **日常使用**: 用户打开`Signal-Dash Pro`桌面应用。**仪表盘**页面展示着后台自动扫描到的新信号。用户手机上的`ntfy`也会同步收到提醒。
2.  **主动决策**: 市场剧烈波动，用户想知道现在是否是介入`ETH/USDT`的好时机。他切换到**手动分析**页面，选择ETH和“震荡策略”，点击分析。几秒钟后，系统告诉他：“建议观望，RSI为55，未到超买超卖区”。
3.  **策略验证**: 用户对趋势策略在SOL上的表现感到好奇。他打开**策略回测**页面，选择`SOL/USDT`、趋势策略，回测过去一年的数据。应用开始在后台计算，进度条稳步前进。几分钟后，一份详细的报告出炉，显示该策略在SOL上过去一年的收益率为`+120%`，最大回撤为`-15%`。这份数据极大地增强了他对自动信号的信心。

这个终极版方案，为您构建了一个功能强大、逻辑闭环、体验专业的个人交易决策平台。