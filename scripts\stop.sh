#!/bin/bash

# Signal-Dash Pro 停止脚本

set -e

echo "🛑 停止 Signal-Dash Pro..."

# 检查Docker Compose是否可用
if ! command -v docker-compose > /dev/null 2>&1; then
    echo "❌ Docker Compose未安装"
    exit 1
fi

# 停止所有服务
echo "🐳 停止Docker服务..."
docker-compose down

echo "✅ Signal-Dash Pro 已停止"

# 可选：清理数据（需要用户确认）
read -p "是否要清理所有数据？(y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🧹 清理数据..."
    docker-compose down -v
    sudo rm -rf data/*
    sudo rm -rf logs/*
    echo "✅ 数据清理完成"
fi
