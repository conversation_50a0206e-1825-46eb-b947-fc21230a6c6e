import { 
  Controller, 
  Get, 
  Post, 
  Put, 
  Delete, 
  Body, 
  Param, 
  HttpException, 
  HttpStatus 
} from '@nestjs/common';
import { GridTradingMemoryService } from './grid-trading-memory.service';
import { CreateGridConfigDto, UpdateGridConfigDto } from '@signal-dash/shared';

@Controller('grid-trading')
export class GridTradingMemoryController {
  constructor(private readonly gridTradingService: GridTradingMemoryService) {}

  // 配置管理
  @Post('configs')
  async createConfig(@Body() dto: CreateGridConfigDto) {
    try {
      return await this.gridTradingService.createConfig(dto);
    } catch (error) {
      throw new HttpException('创建配置失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Get('configs')
  async getConfigs() {
    try {
      return await this.gridTradingService.getConfigs();
    } catch (error) {
      throw new HttpException('获取配置列表失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Get('configs/:id')
  async getConfig(@Param('id') id: string) {
    try {
      const config = await this.gridTradingService.getConfig(id);
      if (!config) {
        throw new HttpException('配置不存在', HttpStatus.NOT_FOUND);
      }
      return config;
    } catch (error) {
      if (error instanceof HttpException) throw error;
      throw new HttpException('获取配置失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Put('configs/:id')
  async updateConfig(@Param('id') id: string, @Body() dto: UpdateGridConfigDto) {
    try {
      const config = await this.gridTradingService.updateConfig(id, dto);
      if (!config) {
        throw new HttpException('配置不存在', HttpStatus.NOT_FOUND);
      }
      return config;
    } catch (error) {
      if (error instanceof HttpException) throw error;
      throw new HttpException('更新配置失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Delete('configs/:id')
  async deleteConfig(@Param('id') id: string) {
    try {
      const deleted = await this.gridTradingService.deleteConfig(id);
      if (!deleted) {
        throw new HttpException('配置不存在', HttpStatus.NOT_FOUND);
      }
      return { message: '删除成功' };
    } catch (error) {
      if (error instanceof HttpException) throw error;
      throw new HttpException('删除配置失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  // 网格控制
  @Post('configs/:id/start')
  async startGrid(@Param('id') id: string) {
    try {
      const success = await this.gridTradingService.startGrid(id);
      if (!success) {
        throw new HttpException('配置不存在', HttpStatus.NOT_FOUND);
      }
      return { message: '网格启动成功' };
    } catch (error) {
      if (error instanceof HttpException) throw error;
      throw new HttpException('启动网格失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Post('configs/:id/stop')
  async stopGrid(@Param('id') id: string) {
    try {
      const success = await this.gridTradingService.stopGrid(id);
      if (!success) {
        throw new HttpException('配置不存在', HttpStatus.NOT_FOUND);
      }
      return { message: '网格停止成功' };
    } catch (error) {
      if (error instanceof HttpException) throw error;
      throw new HttpException('停止网格失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  // 持仓查询
  @Get('configs/:id/positions')
  async getPositions(@Param('id') id: string) {
    try {
      return await this.gridTradingService.getPositions(id);
    } catch (error) {
      throw new HttpException('获取持仓失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  // 订单查询
  @Get('configs/:id/orders')
  async getOrders(@Param('id') id: string) {
    try {
      return await this.gridTradingService.getOrders(id);
    } catch (error) {
      throw new HttpException('获取订单失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  // 性能数据查询
  @Get('configs/:id/performance')
  async getPerformance(@Param('id') id: string) {
    try {
      return await this.gridTradingService.getPerformance(id);
    } catch (error) {
      throw new HttpException('获取性能数据失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
