import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { GridTradingConfig } from './entities/grid-trading-config.entity';
import { TradingPosition } from './entities/trading-position.entity';
import { OrderHistory, OrderStatus } from './entities/order-history.entity';
import { RiskEvent } from './entities/risk-event.entity';
import { StartGridTradingDto } from './dto/start-grid-trading.dto';
import { UpdateGridConfigDto } from './dto/update-grid-config.dto';
import { GridTradingStatus, TradingPerformance } from './interfaces/grid-trading.interface';
import { GridStrategyService } from './strategies/grid-strategy.service';
import { RiskManagerService } from './risk-management/risk-manager.service';
import { ExchangeClientService } from './exchange/exchange-client.service';

@Injectable()
export class GridTradingService {
  private readonly logger = new Logger(GridTradingService.name);
  private readonly activeTradingSessions = new Map<string, any>();

  constructor(
    @InjectRepository(GridTradingConfig)
    private readonly configRepository: Repository<GridTradingConfig>,
    @InjectRepository(TradingPosition)
    private readonly positionRepository: Repository<TradingPosition>,
    @InjectRepository(OrderHistory)
    private readonly orderRepository: Repository<OrderHistory>,
    @InjectRepository(RiskEvent)
    private readonly riskEventRepository: Repository<RiskEvent>,
    @InjectQueue('grid-trading')
    private readonly gridTradingQueue: Queue,
    private readonly gridStrategyService: GridStrategyService,
    private readonly riskManagerService: RiskManagerService,
    private readonly exchangeClientService: ExchangeClientService,
  ) {}

  async startGridTrading(startDto: StartGridTradingDto): Promise<{ jobId: string }> {
    this.logger.log(`启动网格交易: ${startDto.symbol}`);

    try {
      // 1. 验证交易对和参数
      await this.validateTradingParameters(startDto);

      // 2. 检查是否已有活跃的交易会话
      if (this.activeTradingSessions.has(startDto.symbol)) {
        throw new Error(`交易对 ${startDto.symbol} 已有活跃的网格交易会话`);
      }

      // 3. 创建或更新配置
      const config = await this.createOrUpdateConfig(startDto);

      // 4. 初始化交易会话
      const sessionId = `grid-${startDto.symbol}-${Date.now()}`;
      
      // 5. 添加到任务队列
      const job = await this.gridTradingQueue.add('start-grid-trading', {
        sessionId,
        config,
        startDto,
      }, {
        jobId: sessionId,
        removeOnComplete: 10,
        removeOnFail: 5,
      });

      // 6. 记录活跃会话
      this.activeTradingSessions.set(startDto.symbol, {
        sessionId,
        jobId: job.id,
        startTime: new Date(),
        status: 'starting',
      });

      this.logger.log(`网格交易任务已创建: ${sessionId}`);
      return { jobId: sessionId };

    } catch (error) {
      this.logger.error(`启动网格交易失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  async stopGridTrading(symbol: string): Promise<{ success: boolean }> {
    this.logger.log(`停止网格交易: ${symbol}`);

    try {
      const session = this.activeTradingSessions.get(symbol);
      if (!session) {
        throw new Error(`未找到交易对 ${symbol} 的活跃交易会话`);
      }

      // 1. 取消队列中的任务
      const job = await this.gridTradingQueue.getJob(session.jobId);
      if (job) {
        await job.remove();
      }

      // 2. 停止交易逻辑（取消未成交订单等）
      await this.exchangeClientService.cancelAllOrders(symbol);

      // 3. 更新配置状态
      await this.configRepository.update(
        { symbol },
        { 
          isActive: false,
          updatedAt: new Date(),
        }
      );

      // 4. 移除活跃会话
      this.activeTradingSessions.delete(symbol);

      this.logger.log(`网格交易已停止: ${symbol}`);
      return { success: true };

    } catch (error) {
      this.logger.error(`停止网格交易失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  async getAllTradingStatus(): Promise<GridTradingStatus[]> {
    const configs = await this.configRepository.find({
      where: { isActive: true },
      order: { createdAt: 'DESC' },
    });

    const statuses: GridTradingStatus[] = [];
    
    for (const config of configs) {
      const status = await this.buildTradingStatus(config);
      statuses.push(status);
    }

    return statuses;
  }

  async getTradingStatus(symbol: string): Promise<GridTradingStatus> {
    const config = await this.configRepository.findOne({
      where: { symbol },
    });

    if (!config) {
      throw new Error(`未找到交易对 ${symbol} 的配置`);
    }

    return this.buildTradingStatus(config);
  }

  async updateGridConfig(
    symbol: string,
    updateDto: UpdateGridConfigDto,
  ): Promise<GridTradingConfig> {
    const config = await this.configRepository.findOne({
      where: { symbol },
    });

    if (!config) {
      throw new Error(`未找到交易对 ${symbol} 的配置`);
    }

    // 更新配置
    Object.assign(config, updateDto, { updatedAt: new Date() });
    
    return this.configRepository.save(config);
  }

  async getTradingPositions(
    symbol: string,
    limit: number = 50,
  ): Promise<TradingPosition[]> {
    return this.positionRepository.find({
      where: { symbol },
      order: { createdAt: 'DESC' },
      take: limit,
    });
  }

  async getOrderHistory(
    symbol: string,
    limit: number = 100,
  ): Promise<OrderHistory[]> {
    return this.orderRepository.find({
      where: { symbol },
      order: { createdAt: 'DESC' },
      take: limit,
    });
  }

  async getTradingPerformance(symbol: string): Promise<TradingPerformance> {
    // 实现交易表现统计逻辑
    const orders = await this.orderRepository.find({
      where: { symbol, status: OrderStatus.FILLED },
    });

    // 计算总收益、胜率等指标
    const totalTrades = orders.length;
    const profitableTrades = orders.filter(order => order.profit > 0).length;
    const totalProfit = orders.reduce((sum, order) => sum + order.profit, 0);
    const winRate = totalTrades > 0 ? (profitableTrades / totalTrades) * 100 : 0;

    return {
      symbol,
      totalTrades,
      profitableTrades,
      totalProfit,
      winRate,
      averageProfit: totalTrades > 0 ? totalProfit / totalTrades : 0,
    };
  }

  async emergencyStopAll(): Promise<{ stoppedSessions: string[] }> {
    this.logger.warn('执行紧急停止所有网格交易');

    const stoppedSessions: string[] = [];

    for (const [symbol, session] of this.activeTradingSessions.entries()) {
      try {
        await this.stopGridTrading(symbol);
        stoppedSessions.push(symbol);
      } catch (error) {
        this.logger.error(`紧急停止 ${symbol} 失败: ${error.message}`);
      }
    }

    return { stoppedSessions };
  }

  private async validateTradingParameters(startDto: StartGridTradingDto): Promise<void> {
    // 验证交易对是否存在
    const isValidSymbol = await this.exchangeClientService.validateSymbol(startDto.symbol);
    if (!isValidSymbol) {
      throw new Error(`无效的交易对: ${startDto.symbol}`);
    }

    // 验证网格参数
    if (startDto.gridSize <= 0 || startDto.gridSize > 10) {
      throw new Error('网格大小必须在 0-10% 之间');
    }

    // 验证资金参数
    if (startDto.initialCapital <= 0) {
      throw new Error('初始资金必须大于 0');
    }
  }

  private async createOrUpdateConfig(startDto: StartGridTradingDto): Promise<GridTradingConfig> {
    let config = await this.configRepository.findOne({
      where: { symbol: startDto.symbol },
    });

    if (config) {
      // 更新现有配置
      Object.assign(config, startDto, { 
        isActive: true,
        updatedAt: new Date(),
      });
    } else {
      // 创建新配置
      config = this.configRepository.create({
        ...startDto,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      });
    }

    return this.configRepository.save(config);
  }

  private async buildTradingStatus(config: GridTradingConfig): Promise<GridTradingStatus> {
    const session = this.activeTradingSessions.get(config.symbol);
    
    // 获取最新的交易统计
    const performance = await this.getTradingPerformance(config.symbol);
    
    // 获取当前价格和网格状态
    const currentPrice = await this.exchangeClientService.getCurrentPrice(config.symbol);
    const gridStatus = await this.gridStrategyService.getGridStatus(config.symbol);

    return {
      symbol: config.symbol,
      isActive: config.isActive,
      sessionId: session?.sessionId,
      startTime: session?.startTime,
      currentPrice,
      gridSize: config.gridSize,
      gridStatus,
      performance,
      lastUpdate: new Date(),
    };
  }
}
