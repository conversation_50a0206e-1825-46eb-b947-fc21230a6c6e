import { <PERSON><PERSON>ty, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { TradingPair, StrategyType, SignalType, TimeFrame, TechnicalIndicators } from '@signal-dash/shared';

@Entity('signals')
export class SignalEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  timestamp: number;

  @Column()
  asset: TradingPair;

  @Column({
    type: 'text',
    transformer: {
      to: (value: StrategyType) => value,
      from: (value: string) => value as StrategyType,
    },
  })
  strategy: StrategyType;

  @Column({
    type: 'text',
    transformer: {
      to: (value: TimeFrame) => value,
      from: (value: string) => value as TimeFrame,
    },
  })
  timeFrame: TimeFrame;

  @Column({
    type: 'text',
    transformer: {
      to: (value: SignalType) => value,
      from: (value: string) => value as SignalType,
    },
  })
  signalType: SignalType;

  @Column('integer')
  score: number;

  @Column('real')
  price: number;

  @Column('real', { nullable: true })
  stopLoss?: number;

  @Column('real', { nullable: true })
  takeProfit?: number;

  @Column({
    type: 'text',
    transformer: {
      to: (value: string[]) => JSON.stringify(value),
      from: (value: string) => JSON.parse(value),
    },
  })
  reasoning: string[];

  @Column({
    type: 'text',
    transformer: {
      to: (value: TechnicalIndicators) => JSON.stringify(value),
      from: (value: string) => JSON.parse(value),
    },
  })
  indicators: TechnicalIndicators;

  @Column({ default: false })
  notified: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
