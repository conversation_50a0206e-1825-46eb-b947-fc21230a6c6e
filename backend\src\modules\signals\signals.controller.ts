import { Controller, Get, Query } from '@nestjs/common';

@Controller('signals')
export class SignalsController {
  @Get()
  async getSignals(
    @Query('limit') limit: number = 10,
    @Query('offset') offset: number = 0,
    @Query('asset') asset?: string,
    @Query('strategy') strategy?: string,
  ) {
    // 返回模拟数据
    return {
      signals: [],
      total: 0,
    };
  }

  @Get('stats/summary')
  async getSignalStats() {
    // 返回模拟统计数据
    return {
      totalSignals: 0,
      todaySignals: 0,
      signalsByAsset: {
        BTC: 0,
        ETH: 0,
        SOL: 0,
      },
      signalsByStrategy: {
        'trend-following': 0,
        'mean-reversion': 0,
      },
    };
  }
}
