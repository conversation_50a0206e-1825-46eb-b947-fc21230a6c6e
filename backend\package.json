{"name": "@signal-dash/backend", "version": "1.0.0", "description": "Signal-Dash Pro Backend API", "main": "dist/main.js", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs}/**/*.ts\" --fix"}, "dependencies": {"@nestjs/bull": "^10.0.1", "@nestjs/common": "^10.3.0", "@nestjs/config": "^3.1.1", "@nestjs/core": "^10.3.0", "@nestjs/platform-express": "^10.3.0", "@nestjs/platform-socket.io": "^10.3.0", "@nestjs/schedule": "^4.0.0", "@nestjs/typeorm": "^10.0.1", "@nestjs/websockets": "^10.3.0", "@signal-dash/shared": "workspace:*", "axios": "^1.6.2", "better-sqlite3": "^9.6.0", "bull": "^4.12.2", "ccxt": "^4.2.25", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "decimal.js": "^10.4.3", "ioredis": "^5.3.2", "node-cron": "^3.0.3", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "socket.io": "^4.7.4", "typeorm": "^0.3.17", "ws": "^8.16.0"}, "devDependencies": {"@nestjs/cli": "^10.2.1", "@nestjs/schematics": "^10.0.3", "@types/express": "^4.17.21", "@types/node": "^20.10.5", "@types/node-cron": "^3.0.11", "@types/ws": "^8.5.10", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.2", "prettier": "^3.1.1", "typescript": "^5.3.3"}}