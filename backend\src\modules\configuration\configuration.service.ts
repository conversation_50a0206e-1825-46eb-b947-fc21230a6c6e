import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { SystemConfigEntity } from '../../entities/system-config.entity';
import { SystemConfig, SUPPORTED_PAIRS } from '@signal-dash/shared';
import { UpdateConfigDto } from './dto/update-config.dto';
import { BinanceApiService } from '../../common/services/binance-api.service';
import { NotificationService } from '../../common/services/notification.service';

@Injectable()
export class ConfigurationService {
  private readonly logger = new Logger(ConfigurationService.name);

  constructor(
    @InjectRepository(SystemConfigEntity)
    private configRepository: Repository<SystemConfigEntity>,
    private binanceApi: BinanceApiService,
    private notificationService: NotificationService
  ) {}

  /**
   * 获取系统配置
   */
  async getSystemConfig(): Promise<SystemConfig> {
    const configs = await this.configRepository.find();
    const configMap = new Map(configs.map(c => [c.key, JSON.parse(c.value)]));

    return {
      ntfy: configMap.get('ntfy') || {
        url: 'https://ntfy.sh',
        topic: 'signal-dash-pro',
        enabled: false,
      },
      binance: configMap.get('binance') || {
        apiUrl: 'https://api.binance.com',
        testnet: false,
      },
      monitoring: configMap.get('monitoring') || {
        enabled: true,
        interval: 15,
        assets: SUPPORTED_PAIRS,
      },
    };
  }

  /**
   * 更新系统配置
   */
  async updateSystemConfig(updateDto: UpdateConfigDto): Promise<void> {
    const currentConfig = await this.getSystemConfig();

    // 合并配置
    if (updateDto.ntfy) {
      const newNtfyConfig = { ...currentConfig.ntfy, ...updateDto.ntfy };
      await this.saveConfigItem('ntfy', newNtfyConfig);
    }

    if (updateDto.binance) {
      const newBinanceConfig = { ...currentConfig.binance, ...updateDto.binance };
      await this.saveConfigItem('binance', newBinanceConfig);
    }

    if (updateDto.monitoring) {
      const newMonitoringConfig = { ...currentConfig.monitoring, ...updateDto.monitoring };
      await this.saveConfigItem('monitoring', newMonitoringConfig);
    }

    this.logger.log('系统配置已更新');
  }

  /**
   * 保存配置项
   */
  private async saveConfigItem(key: string, value: any): Promise<void> {
    const existing = await this.configRepository.findOne({ where: { key } });
    
    if (existing) {
      existing.value = JSON.stringify(value);
      await this.configRepository.save(existing);
    } else {
      const newConfig = this.configRepository.create({
        key,
        value: JSON.stringify(value),
        description: this.getConfigDescription(key),
      });
      await this.configRepository.save(newConfig);
    }
  }

  /**
   * 获取配置描述
   */
  private getConfigDescription(key: string): string {
    const descriptions = {
      ntfy: 'ntfy推送通知配置',
      binance: '币安API配置',
      monitoring: '自动监控配置',
    };
    return descriptions[key] || '';
  }

  /**
   * 测试通知功能
   */
  async testNotification(): Promise<boolean> {
    return await this.notificationService.testNotification();
  }

  /**
   * 获取系统状态
   */
  async getSystemStatus(): Promise<{
    database: boolean;
    binanceApi: boolean;
    redis: boolean;
    notification: boolean;
  }> {
    const status = {
      database: false,
      binanceApi: false,
      redis: false,
      notification: false,
    };

    // 测试数据库连接
    try {
      await this.configRepository.count();
      status.database = true;
    } catch (error) {
      this.logger.error('数据库连接测试失败:', error);
    }

    // 测试币安API连接
    try {
      status.binanceApi = await this.binanceApi.ping();
    } catch (error) {
      this.logger.error('币安API连接测试失败:', error);
    }

    // 测试Redis连接 (简化版本，实际应该测试Redis连接)
    status.redis = true;

    // 测试通知功能
    try {
      status.notification = await this.notificationService.testNotification();
    } catch (error) {
      this.logger.error('通知功能测试失败:', error);
    }

    return status;
  }

  /**
   * 初始化默认配置
   */
  async initializeDefaultConfig(): Promise<void> {
    const existingConfigs = await this.configRepository.count();
    
    if (existingConfigs === 0) {
      this.logger.log('初始化默认配置...');
      
      const defaultConfig: SystemConfig = {
        ntfy: {
          url: 'https://ntfy.sh',
          topic: 'signal-dash-pro',
          enabled: false,
        },
        binance: {
          apiUrl: 'https://api.binance.com',
          testnet: false,
        },
        monitoring: {
          enabled: true,
          interval: 15,
          assets: SUPPORTED_PAIRS,
        },
      };

      await this.saveConfigItem('ntfy', defaultConfig.ntfy);
      await this.saveConfigItem('binance', defaultConfig.binance);
      await this.saveConfigItem('monitoring', defaultConfig.monitoring);
      
      this.logger.log('默认配置初始化完成');
    }
  }
}
