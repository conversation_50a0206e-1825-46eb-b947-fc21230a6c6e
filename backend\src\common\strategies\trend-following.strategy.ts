import { Kline, TradingPair, StrategyType, SignalType, TimeFrame } from '@signal-dash/shared';
import { STRATEGY_PARAMS } from '@signal-dash/shared';
import { BaseStrategy, StrategyAnalysisResult } from './base.strategy';

/**
 * 趋势跟踪策略 (H4时间框架)
 * 
 * 策略逻辑：
 * 1. 前提条件：EMA(50) > EMA(200) (多头趋势) 或 EMA(50) < EMA(200) (空头趋势)
 * 2. 触发逻辑：
 *    - 多头：价格回调触及EMA(20)，随后出现看涨吞没K线
 *    - 空头：价格反弹触及EMA(20)，随后出现看跌吞没K线
 * 3. 风控：止损设置在吞没K线极值±0.25*ATR处
 */
export class TrendFollowingStrategy extends BaseStrategy {
  constructor() {
    super(StrategyType.TREND_FOLLOWING, STRATEGY_PARAMS.TREND_FOLLOWING.TIMEFRAME);
  }
  
  analyze(klines: Kline[], asset: TradingPair): StrategyAnalysisResult {
    const indicators = this.technicalAnalysis.calculateIndicators(klines);
    if (!indicators) {
      return {
        signal: SignalType.HOLD,
        score: 0,
        reasoning: ['数据不足，无法计算技术指标']
      };
    }
    
    const currentPrice = klines[klines.length - 1].close;
    const trendDirection = this.technicalAnalysis.getTrendDirection(indicators);
    const engulfingPattern = this.technicalAnalysis.detectEngulfingPattern(klines);
    const isPriceTouchingEMA20 = this.technicalAnalysis.isPriceTouchingEMA(currentPrice, indicators.ema20);
    
    const reasoning: string[] = [];
    const scoreFactors: { [key: string]: number } = {};
    
    // 1. 趋势方向评分 (30分)
    if (trendDirection === 'BULLISH') {
      scoreFactors.trend = 30;
      this.addReasoning(reasoning, '宏观趋势向上 (EMA50 > EMA200)', 30);
    } else if (trendDirection === 'BEARISH') {
      scoreFactors.trend = 30;
      this.addReasoning(reasoning, '宏观趋势向下 (EMA50 < EMA200)', 30);
    } else {
      scoreFactors.trend = -20;
      this.addReasoning(reasoning, '趋势不明确', -20);
    }
    
    // 2. EMA20回调/反弹评分 (25分)
    if (isPriceTouchingEMA20) {
      scoreFactors.emaTouch = 25;
      this.addReasoning(reasoning, '价格触及EMA20动态支撑/阻力', 25);
    } else {
      scoreFactors.emaTouch = -15;
      this.addReasoning(reasoning, '价格未触及EMA20', -15);
    }
    
    // 3. 吞没K线形态评分 (35分)
    if (trendDirection === 'BULLISH' && engulfingPattern.isBullishEngulfing) {
      scoreFactors.engulfing = 35;
      this.addReasoning(reasoning, '出现看涨吞没K线形态', 35);
    } else if (trendDirection === 'BEARISH' && engulfingPattern.isBearishEngulfing) {
      scoreFactors.engulfing = 35;
      this.addReasoning(reasoning, '出现看跌吞没K线形态', 35);
    } else {
      scoreFactors.engulfing = -25;
      this.addReasoning(reasoning, '未出现有效吞没形态', -25);
    }
    
    // 4. 成交量确认 (10分)
    const volumeConfirmation = this.checkVolumeConfirmation(klines);
    if (volumeConfirmation) {
      scoreFactors.volume = 10;
      this.addReasoning(reasoning, '成交量放大确认', 10);
    } else {
      scoreFactors.volume = -5;
      this.addReasoning(reasoning, '成交量未放大', -5);
    }
    
    const totalScore = this.calculateScore(scoreFactors);
    
    // 确定信号类型
    let signal: SignalType = SignalType.HOLD;
    let stopLoss: number | undefined;
    let takeProfit: number | undefined;
    
    if (totalScore >= STRATEGY_PARAMS.TREND_FOLLOWING.MIN_SCORE) {
      if (trendDirection === 'BULLISH' && engulfingPattern.isBullishEngulfing) {
        signal = SignalType.BUY;
        stopLoss = this.calculateBuyStopLoss(klines, indicators.atr);
      } else if (trendDirection === 'BEARISH' && engulfingPattern.isBearishEngulfing) {
        signal = SignalType.SELL;
        stopLoss = this.calculateSellStopLoss(klines, indicators.atr);
      }
    }
    
    return {
      signal,
      score: totalScore,
      reasoning,
      stopLoss,
      takeProfit
    };
  }
  
  /**
   * 检查成交量确认
   */
  private checkVolumeConfirmation(klines: Kline[]): boolean {
    if (klines.length < 5) return false;
    
    const currentVolume = klines[klines.length - 1].volume;
    const avgVolume = klines.slice(-5, -1).reduce((sum, k) => sum + k.volume, 0) / 4;
    
    return currentVolume > avgVolume * 1.2; // 成交量比平均值大20%以上
  }
  
  /**
   * 计算买入止损价
   */
  private calculateBuyStopLoss(klines: Kline[], atr: number): number {
    if (klines.length < 2) return 0;
    
    // 吞没K线组合的最低点
    const engulfingLow = Math.min(
      klines[klines.length - 1].low,
      klines[klines.length - 2].low
    );
    
    return engulfingLow - (atr * STRATEGY_PARAMS.TREND_FOLLOWING.ATR_MULTIPLIER);
  }
  
  /**
   * 计算卖出止损价
   */
  private calculateSellStopLoss(klines: Kline[], atr: number): number {
    if (klines.length < 2) return 0;
    
    // 吞没K线组合的最高点
    const engulfingHigh = Math.max(
      klines[klines.length - 1].high,
      klines[klines.length - 2].high
    );
    
    return engulfingHigh + (atr * STRATEGY_PARAMS.TREND_FOLLOWING.ATR_MULTIPLIER);
  }
}
