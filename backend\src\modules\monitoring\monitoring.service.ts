import { Injectable, Logger } from '@nestjs/common';
import { <PERSON>ron } from '@nestjs/schedule';
import { TradingSignal, StrategyType, TradingPair, SignalType } from '@signal-dash/shared';
import { MarketDataService } from '../../common/services/market-data.service';
import { StrategyService } from '../../common/services/strategy.service';
import { WebsocketGateway } from '../websocket/websocket.gateway';

export interface MonitoringResult {
  id: string;
  asset: TradingPair;
  strategy: StrategyType;
  signal: TradingSignal | null;
  timestamp: Date;
  isQualified: boolean; // 是否符合记录要求
  reason?: string; // 不符合要求的原因
}

@Injectable()
export class MonitoringService {
  private readonly logger = new Logger(MonitoringService.name);
  private readonly SUPPORTED_PAIRS: TradingPair[] = ['BTC/USDT', 'ETH/USDT', 'SOL/USDT'];
  private readonly STRATEGIES = [StrategyType.TREND_FOLLOWING, StrategyType.MEAN_REVERSION];
  
  // 监控配置
  private monitoringConfig = {
    enabled: true,
    interval: 5, // 5分钟
    minScore: 70, // 最低评分要求
    qualityThreshold: {
      minScore: 70,
      requireValidSignal: true,
      excludeHoldSignals: true,
    }
  };

  constructor(
    private readonly marketDataService: MarketDataService,
    private readonly strategyService: StrategyService,
    private readonly websocketGateway: WebsocketGateway,
  ) {}

  /**
   * 定时监控任务 - 每5分钟执行一次
   */
  @Cron('0 */5 * * * *')
  async executeMonitoring(): Promise<void> {
    if (!this.monitoringConfig.enabled) {
      return;
    }

    this.logger.log('🔍 开始执行定时监控...');
    
    try {
      const results: MonitoringResult[] = [];
      
      for (const asset of this.SUPPORTED_PAIRS) {
        for (const strategy of this.STRATEGIES) {
          const result = await this.analyzeAssetWithStrategy(asset, strategy);
          results.push(result);
          
          // 短暂延迟避免API请求过于频繁
          await this.delay(500);
        }
      }

      // 推送监控结果到前端
      this.broadcastMonitoringResults(results);
      
      // 记录符合要求的信号
      const qualifiedResults = results.filter(r => r.isQualified);
      this.logger.log(`监控完成: 总计${results.length}个分析，${qualifiedResults.length}个符合记录要求`);
      
      // 这里可以选择性地保存到数据库
      // await this.saveQualifiedSignals(qualifiedResults);
      
    } catch (error) {
      this.logger.error('监控执行失败:', error);
    }
  }

  /**
   * 分析单个资产的单个策略
   */
  private async analyzeAssetWithStrategy(
    asset: TradingPair, 
    strategy: StrategyType
  ): Promise<MonitoringResult> {
    const resultId = `${asset}-${strategy}-${Date.now()}`;
    
    try {
      // 获取对应时间框架的数据
      const timeFrame = strategy === StrategyType.TREND_FOLLOWING ? '4h' : '15m';
      const klines = await this.marketDataService.getKlines(asset, timeFrame as any, 300, true);
      
      if (klines.length < 200) {
        return {
          id: resultId,
          asset,
          strategy,
          signal: null,
          timestamp: new Date(),
          isQualified: false,
          reason: `数据不足，需要至少200根K线，当前只有${klines.length}根`
        };
      }

      // 执行策略分析
      const signal = this.strategyService.analyzeWithStrategy(strategy, klines, asset);
      
      // 判断是否符合记录要求
      const { isQualified, reason } = this.evaluateSignalQuality(signal);
      
      this.logger.debug(`${asset} ${strategy}: ${signal ? `${signal.signalType} (${signal.score})` : '无信号'} - ${isQualified ? '符合要求' : reason}`);
      
      return {
        id: resultId,
        asset,
        strategy,
        signal,
        timestamp: new Date(),
        isQualified,
        reason
      };
      
    } catch (error) {
      this.logger.warn(`分析${asset}使用${strategy}策略失败:`, error.message);
      
      return {
        id: resultId,
        asset,
        strategy,
        signal: null,
        timestamp: new Date(),
        isQualified: false,
        reason: `分析失败: ${error.message}`
      };
    }
  }

  /**
   * 评估信号质量
   */
  private evaluateSignalQuality(signal: TradingSignal | null): { isQualified: boolean; reason?: string } {
    if (!signal) {
      return { isQualified: false, reason: '未生成信号' };
    }

    if (this.monitoringConfig.qualityThreshold.excludeHoldSignals && signal.signalType === SignalType.HOLD) {
      return { isQualified: false, reason: '观望信号不记录' };
    }

    if (signal.score < this.monitoringConfig.qualityThreshold.minScore) {
      return { isQualified: false, reason: `评分过低 (${signal.score} < ${this.monitoringConfig.qualityThreshold.minScore})` };
    }

    return { isQualified: true };
  }

  /**
   * 广播监控结果到前端
   */
  private broadcastMonitoringResults(results: MonitoringResult[]): void {
    // 发送完整的监控结果
    this.websocketGateway.broadcast('monitoring:results', {
      timestamp: new Date(),
      results: results.map(r => ({
        id: r.id,
        asset: r.asset,
        strategy: r.strategy,
        signal: r.signal ? {
          signalType: r.signal.signalType,
          score: r.signal.score,
          price: r.signal.price,
          stopLoss: r.signal.stopLoss,
          takeProfit: r.signal.takeProfit,
        } : null,
        isQualified: r.isQualified,
        reason: r.reason,
        timestamp: r.timestamp,
      }))
    });

    // 发送符合要求的信号（用于信号列表）
    const qualifiedSignals = results.filter(r => r.isQualified && r.signal);
    qualifiedSignals.forEach(result => {
      if (result.signal) {
        this.websocketGateway.broadcastSignal(result.signal);
      }
    });
  }

  /**
   * 获取当前监控配置
   */
  getMonitoringConfig() {
    return {
      ...this.monitoringConfig,
      supportedPairs: this.SUPPORTED_PAIRS,
      strategies: this.STRATEGIES,
    };
  }

  /**
   * 更新监控配置
   */
  updateMonitoringConfig(config: Partial<typeof this.monitoringConfig>) {
    this.monitoringConfig = { ...this.monitoringConfig, ...config };
    this.logger.log('监控配置已更新:', this.monitoringConfig);
  }

  /**
   * 手动触发监控
   */
  async triggerManualMonitoring(): Promise<MonitoringResult[]> {
    this.logger.log('🔍 手动触发监控...');
    
    const results: MonitoringResult[] = [];
    
    for (const asset of this.SUPPORTED_PAIRS) {
      for (const strategy of this.STRATEGIES) {
        const result = await this.analyzeAssetWithStrategy(asset, strategy);
        results.push(result);
        await this.delay(500);
      }
    }

    this.broadcastMonitoringResults(results);
    return results;
  }

  /**
   * 延迟函数
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
