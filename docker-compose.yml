version: '3.8'

services:
  signal-dash-pro:
    build: .
    ports:
      - "3000:3000"  # 后端API端口
      - "3001:3001"  # 前端端口
    environment:
      - NODE_ENV=production
      - DATABASE_PATH=/app/data/signals.db
      - BINANCE_API_URL=https://api.binance.com
      - NTFY_URL=https://ntfy.sh
    volumes:
      - ./data:/app/data
      - ./config:/app/config
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 可选：Redis用于缓存和任务队列
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

volumes:
  redis_data:
