import { Kline, TradingPair, StrategyType, SignalType, TimeFrame } from '@signal-dash/shared';
import { STRATEGY_PARAMS, INDICATOR_PARAMS } from '@signal-dash/shared';
import { BaseStrategy, StrategyAnalysisResult } from './base.strategy';
import { BollingerBandsIndicator } from '../indicators';

/**
 * 震荡区间策略 (M15时间框架)
 * 
 * 策略逻辑：
 * 1. 前提条件：布林带带宽<4%，中轨斜率接近0 (震荡状态)
 * 2. 触发逻辑：
 *    - 买入：价格触及布林带下轨 且 RSI<30
 *    - 卖出：价格触及布林带上轨 且 RSI>70
 * 3. 风控：止损在入场K线极值±1*ATR处，止盈在布林带中轨
 */
export class MeanReversionStrategy extends BaseStrategy {
  constructor() {
    super(StrategyType.MEAN_REVERSION, STRATEGY_PARAMS.MEAN_REVERSION.TIMEFRAME);
  }
  
  analyze(klines: Kline[], asset: TradingPair): StrategyAnalysisResult {
    const indicators = this.technicalAnalysis.calculateIndicators(klines);
    if (!indicators) {
      return {
        signal: SignalType.HOLD,
        score: 0,
        reasoning: ['数据不足，无法计算技术指标']
      };
    }
    
    const currentKline = klines[klines.length - 1];
    const currentPrice = currentKline.close;
    
    // 创建布林带指标实例用于详细分析
    const bb = new BollingerBandsIndicator(
      INDICATOR_PARAMS.BOLLINGER_BANDS.PERIOD,
      INDICATOR_PARAMS.BOLLINGER_BANDS.MULTIPLIER
    );
    bb.update(klines);
    
    const reasoning: string[] = [];
    const scoreFactors: { [key: string]: number } = {};
    
    // 1. 震荡状态确认 (30分)
    const isConsolidating = bb.isConsolidating(STRATEGY_PARAMS.MEAN_REVERSION.BANDWIDTH_THRESHOLD);
    if (isConsolidating) {
      scoreFactors.consolidation = 30;
      this.addReasoning(reasoning, `市场处于震荡状态 (带宽: ${(indicators.bollingerBands.bandwidth * 100).toFixed(2)}%)`, 30);
    } else {
      scoreFactors.consolidation = -30;
      this.addReasoning(reasoning, `市场趋势性较强 (带宽: ${(indicators.bollingerBands.bandwidth * 100).toFixed(2)}%)`, -30);
    }
    
    // 2. 中轨斜率评分 (15分)
    const middleBandSlope = bb.getMiddleBandSlope(5);
    if (middleBandSlope !== null && Math.abs(middleBandSlope) < 0.01) {
      scoreFactors.slope = 15;
      this.addReasoning(reasoning, '布林带中轨斜率平缓', 15);
    } else {
      scoreFactors.slope = -10;
      this.addReasoning(reasoning, '布林带中轨斜率较陡', -10);
    }
    
    // 3. 布林带触及评分 (25分)
    const isTouchingUpper = bb.isTouchingUpperBand(currentKline.high);
    const isTouchingLower = bb.isTouchingLowerBand(currentKline.low);
    
    if (isTouchingUpper) {
      scoreFactors.bandTouch = 25;
      this.addReasoning(reasoning, '价格触及布林带上轨', 25);
    } else if (isTouchingLower) {
      scoreFactors.bandTouch = 25;
      this.addReasoning(reasoning, '价格触及布林带下轨', 25);
    } else {
      scoreFactors.bandTouch = -20;
      this.addReasoning(reasoning, '价格未触及布林带边界', -20);
    }
    
    // 4. RSI超买超卖评分 (25分)
    const isRSIOverbought = indicators.rsi > INDICATOR_PARAMS.RSI.OVERBOUGHT;
    const isRSIOversold = indicators.rsi < INDICATOR_PARAMS.RSI.OVERSOLD;
    
    if (isRSIOverbought && isTouchingUpper) {
      scoreFactors.rsi = 25;
      this.addReasoning(reasoning, `RSI超买 (${indicators.rsi.toFixed(1)})`, 25);
    } else if (isRSIOversold && isTouchingLower) {
      scoreFactors.rsi = 25;
      this.addReasoning(reasoning, `RSI超卖 (${indicators.rsi.toFixed(1)})`, 25);
    } else {
      scoreFactors.rsi = -15;
      this.addReasoning(reasoning, `RSI未达到极值 (${indicators.rsi.toFixed(1)})`, -15);
    }
    
    // 5. 价格位置评分 (5分)
    const pricePosition = this.calculatePricePosition(currentPrice, indicators.bollingerBands);
    if (pricePosition < 0.2 || pricePosition > 0.8) {
      scoreFactors.position = 5;
      this.addReasoning(reasoning, '价格位于布林带极端位置', 5);
    }
    
    const totalScore = this.calculateScore(scoreFactors);
    
    // 确定信号类型
    let signal: SignalType = SignalType.HOLD;
    let stopLoss: number | undefined;
    let takeProfit: number | undefined;
    
    if (totalScore >= STRATEGY_PARAMS.MEAN_REVERSION.MIN_SCORE) {
      if (isTouchingLower && isRSIOversold) {
        signal = SignalType.BUY;
        stopLoss = currentKline.low - (indicators.atr * STRATEGY_PARAMS.MEAN_REVERSION.ATR_MULTIPLIER);
        takeProfit = indicators.bollingerBands.middle;
      } else if (isTouchingUpper && isRSIOverbought) {
        signal = SignalType.SELL;
        stopLoss = currentKline.high + (indicators.atr * STRATEGY_PARAMS.MEAN_REVERSION.ATR_MULTIPLIER);
        takeProfit = indicators.bollingerBands.middle;
      }
    }
    
    return {
      signal,
      score: totalScore,
      reasoning,
      stopLoss,
      takeProfit
    };
  }
  
  /**
   * 计算价格在布林带中的相对位置
   * 0 = 下轨, 0.5 = 中轨, 1 = 上轨
   */
  private calculatePricePosition(price: number, bands: { upper: number; middle: number; lower: number }): number {
    const { upper, lower } = bands;
    return (price - lower) / (upper - lower);
  }
}
