import { Module } from '@nestjs/common';
import { MonitoringService } from './monitoring.service';
import { MonitoringController } from './monitoring.controller';
import { MarketDataModule } from '../../common/services/market-data.module';
import { StrategyModule } from '../../common/services/strategy.module';
import { WebsocketModule } from '../websocket/websocket.module';

@Module({
  imports: [
    MarketDataModule,
    StrategyModule,
    WebsocketModule,
  ],
  controllers: [MonitoringController],
  providers: [MonitoringService],
  exports: [MonitoringService],
})
export class MonitoringModule {}
