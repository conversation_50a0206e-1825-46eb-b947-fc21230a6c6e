import { Controller, Post, Get, Body, Param, HttpException, HttpStatus } from '@nestjs/common';
import { BacktestService } from './backtest.service';
import { BacktestRequestDto } from './dto/backtest-request.dto';
import { BacktestJob, BacktestResult } from '@signal-dash/shared';

@Controller('backtest')
export class BacktestController {
  constructor(private readonly backtestService: BacktestService) {}

  @Post()
  async startBacktest(@Body() request: BacktestRequestDto): Promise<{ jobId: string }> {
    try {
      const jobId = await this.backtestService.startBacktest(request);
      return { jobId };
    } catch (error) {
      throw new HttpException(
        `启动回测失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get(':jobId/status')
  async getBacktestStatus(@Param('jobId') jobId: string): Promise<BacktestJob> {
    try {
      const job = await this.backtestService.getBacktestStatus(jobId);
      if (!job) {
        throw new HttpException('回测任务不存在', HttpStatus.NOT_FOUND);
      }
      return job;
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        `获取回测状态失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get(':jobId/results')
  async getBacktestResults(@Param('jobId') jobId: string): Promise<BacktestResult> {
    try {
      const result = await this.backtestService.getBacktestResults(jobId);
      if (!result) {
        throw new HttpException('回测结果不存在或未完成', HttpStatus.NOT_FOUND);
      }
      return result;
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        `获取回测结果失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }
}
