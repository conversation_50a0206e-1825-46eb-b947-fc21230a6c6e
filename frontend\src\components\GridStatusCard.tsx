import { TrendingUp, TrendingDown, Activity, AlertTriangle, DollarSign, Target } from 'lucide-react';

interface GridStatusCardProps {
  status: {
    isRunning: boolean;
    sessionId?: string;
    startTime?: string;
    gridStatus?: {
      upperBand: number;
      lowerBand: number;
      currentLevel: number;
      totalLevels: number;
      activeBuyOrders: number;
      activeSellOrders: number;
      nextBuyPrice: number;
      nextSellPrice: number;
    };
    riskMetrics?: {
      currentPositionRatio: number;
      maxPositionRatio: number;
      consecutiveFailures: number;
      maxConsecutiveFailures: number;
      lastRiskCheck: string;
      riskLevel: 'low' | 'medium' | 'high' | 'critical';
      warnings: string[];
    };
    positionSummary?: {
      symbol: string;
      totalPositions: number;
      openPositions: number;
      closedPositions: number;
      totalValue: number;
      unrealizedPnl: number;
      realizedPnl: number;
      averageEntryPrice: number;
      currentPrice: number;
    };
  };
  isLoading: boolean;
}

export function GridStatusCard({ status, isLoading }: GridStatusCardProps) {
  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {[...Array(4)].map((_, i) => (
          <div key={i} className="bg-white rounded-lg shadow p-6 animate-pulse">
            <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
            <div className="h-8 bg-gray-200 rounded w-1/2"></div>
          </div>
        ))}
      </div>
    );
  }

  if (!status.isRunning) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="text-center py-8">
          <Activity className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">网格交易未运行</h3>
          <p className="text-gray-600">点击"开始交易"按钮启动网格交易系统</p>
        </div>
      </div>
    );
  }

  const { gridStatus, riskMetrics, positionSummary } = status;

  // 计算运行时长
  const getRunningTime = () => {
    if (!status.startTime) return '未知';
    const start = new Date(status.startTime);
    const now = new Date();
    const diff = now.getTime() - start.getTime();
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    return `${hours}小时${minutes}分钟`;
  };

  // 获取风险级别颜色
  const getRiskLevelColor = (level: string) => {
    switch (level) {
      case 'low': return 'text-green-600 bg-green-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'high': return 'text-orange-600 bg-orange-100';
      case 'critical': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  // 获取风险级别文本
  const getRiskLevelText = (level: string) => {
    switch (level) {
      case 'low': return '低风险';
      case 'medium': return '中等风险';
      case 'high': return '高风险';
      case 'critical': return '严重风险';
      default: return '未知';
    }
  };

  return (
    <div className="space-y-6">
      {/* 主要状态卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* 网格状态 */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Target className="h-8 w-8 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">网格状态</p>
              <p className="text-2xl font-semibold text-gray-900">
                {gridStatus ? `${gridStatus.currentLevel}/${gridStatus.totalLevels}` : 'N/A'}
              </p>
              <p className="text-xs text-gray-500">
                运行时长: {getRunningTime()}
              </p>
            </div>
          </div>
        </div>

        {/* 活跃订单 */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Activity className="h-8 w-8 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">活跃订单</p>
              <div className="flex items-center space-x-2">
                <span className="text-lg font-semibold text-green-600">
                  {gridStatus?.activeBuyOrders || 0}
                </span>
                <span className="text-sm text-gray-500">买</span>
                <span className="text-lg font-semibold text-red-600">
                  {gridStatus?.activeSellOrders || 0}
                </span>
                <span className="text-sm text-gray-500">卖</span>
              </div>
            </div>
          </div>
        </div>

        {/* 仓位价值 */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <DollarSign className="h-8 w-8 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">仓位价值</p>
              <p className="text-2xl font-semibold text-gray-900">
                ${positionSummary?.totalValue.toLocaleString(undefined, { maximumFractionDigits: 2 }) || '0'}
              </p>
              <p className="text-xs text-gray-500">
                {positionSummary?.openPositions || 0} 个开仓
              </p>
            </div>
          </div>
        </div>

        {/* 盈亏状态 */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              {(positionSummary?.unrealizedPnl || 0) >= 0 ? (
                <TrendingUp className="h-8 w-8 text-green-600" />
              ) : (
                <TrendingDown className="h-8 w-8 text-red-600" />
              )}
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">未实现盈亏</p>
              <p className={`text-2xl font-semibold ${
                (positionSummary?.unrealizedPnl || 0) >= 0 ? 'text-green-600' : 'text-red-600'
              }`}>
                {(positionSummary?.unrealizedPnl || 0) >= 0 ? '+' : ''}
                ${positionSummary?.unrealizedPnl.toLocaleString(undefined, { maximumFractionDigits: 2 }) || '0'}
              </p>
              <p className="text-xs text-gray-500">
                已实现: ${positionSummary?.realizedPnl.toLocaleString(undefined, { maximumFractionDigits: 2 }) || '0'}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* 详细信息面板 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 网格详情 */}
        {gridStatus && (
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">网格详情</h3>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">上轨价格:</span>
                <span className="text-sm font-medium text-gray-900">
                  ${gridStatus.upperBand.toLocaleString(undefined, { maximumFractionDigits: 4 })}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">下轨价格:</span>
                <span className="text-sm font-medium text-gray-900">
                  ${gridStatus.lowerBand.toLocaleString(undefined, { maximumFractionDigits: 4 })}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">下次买入价:</span>
                <span className="text-sm font-medium text-green-600">
                  ${gridStatus.nextBuyPrice.toLocaleString(undefined, { maximumFractionDigits: 4 })}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">下次卖出价:</span>
                <span className="text-sm font-medium text-red-600">
                  ${gridStatus.nextSellPrice.toLocaleString(undefined, { maximumFractionDigits: 4 })}
                </span>
              </div>
            </div>
          </div>
        )}

        {/* 风险监控 */}
        {riskMetrics && (
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">风险监控</h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">风险级别:</span>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getRiskLevelColor(riskMetrics.riskLevel)}`}>
                  {getRiskLevelText(riskMetrics.riskLevel)}
                </span>
              </div>
              
              <div>
                <div className="flex justify-between text-sm mb-1">
                  <span className="text-gray-600">仓位比例:</span>
                  <span className="font-medium">
                    {(riskMetrics.currentPositionRatio * 100).toFixed(1)}% / {(riskMetrics.maxPositionRatio * 100).toFixed(1)}%
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className={`h-2 rounded-full ${
                      riskMetrics.currentPositionRatio > riskMetrics.maxPositionRatio * 0.8 
                        ? 'bg-red-600' 
                        : riskMetrics.currentPositionRatio > riskMetrics.maxPositionRatio * 0.6
                        ? 'bg-yellow-600'
                        : 'bg-green-600'
                    }`}
                    style={{ width: `${Math.min((riskMetrics.currentPositionRatio / riskMetrics.maxPositionRatio) * 100, 100)}%` }}
                  />
                </div>
              </div>

              <div className="flex justify-between">
                <span className="text-sm text-gray-600">连续失败:</span>
                <span className="text-sm font-medium text-gray-900">
                  {riskMetrics.consecutiveFailures} / {riskMetrics.maxConsecutiveFailures}
                </span>
              </div>

              {riskMetrics.warnings.length > 0 && (
                <div className="mt-4">
                  <div className="flex items-center mb-2">
                    <AlertTriangle className="h-4 w-4 text-yellow-500 mr-1" />
                    <span className="text-sm font-medium text-yellow-700">风险警告</span>
                  </div>
                  <ul className="text-xs text-yellow-600 space-y-1">
                    {riskMetrics.warnings.map((warning, index) => (
                      <li key={index}>• {warning}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
