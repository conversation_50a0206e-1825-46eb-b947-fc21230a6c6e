import { Injectable, Logger } from '@nestjs/common';
import { 
  BacktestRequest, 
  BacktestResult, 
  BacktestTrade, 
  Kline, 
  SignalType,
  STRATEGY_PARAMS 
} from '@signal-dash/shared';
import { MarketDataService } from '../../common/services/market-data.service';
import { StrategyService } from '../../common/services/strategy.service';
import { generateId } from '@signal-dash/shared';

/**
 * 回测引擎服务
 */
@Injectable()
export class BacktestEngineService {
  private readonly logger = new Logger(BacktestEngineService.name);

  constructor(
    private marketDataService: MarketDataService,
    private strategyService: StrategyService
  ) {}

  /**
   * 执行回测
   */
  async runBacktest(
    request: BacktestRequest,
    progressCallback?: (progress: number) => void
  ): Promise<BacktestResult> {
    const { asset, strategy, startDate, endDate, initialCapital } = request;
    
    this.logger.log(`开始回测: ${asset} ${strategy} ${startDate} - ${endDate}`);

    // 获取策略时间框架
    const timeFrame = STRATEGY_PARAMS[strategy].TIMEFRAME;
    
    // 获取历史数据
    const startTime = new Date(startDate).getTime();
    const endTime = new Date(endDate).getTime();
    
    const klines = await this.marketDataService.getHistoricalData(
      asset,
      timeFrame,
      startTime,
      endTime
    );

    if (klines.length < 200) {
      throw new Error(`历史数据不足，需要至少200根K线，当前只有${klines.length}根`);
    }

    // 执行回测模拟
    const trades: BacktestTrade[] = [];
    const equityCurve: Array<{ timestamp: number; equity: number }> = [];
    
    let currentCapital = initialCapital;
    let position: 'LONG' | 'SHORT' | 'NONE' = 'NONE';
    let entryPrice = 0;
    let entryTime = 0;
    let stopLoss = 0;
    let takeProfit = 0;

    // 从第200根K线开始分析（确保有足够数据计算指标）
    for (let i = 200; i < klines.length; i++) {
      const currentKlines = klines.slice(0, i + 1);
      const currentKline = klines[i];
      
      // 更新进度
      if (progressCallback) {
        const progress = Math.floor(((i - 200) / (klines.length - 200)) * 100);
        progressCallback(progress);
      }

      try {
        // 生成交易信号
        const signal = this.strategyService.analyzeWithStrategy(strategy, currentKlines, asset);
        
        if (!signal) continue;

        // 处理交易逻辑
        if (position === 'NONE') {
          // 无持仓时检查入场信号
          if (signal.signalType === SignalType.BUY && signal.score >= STRATEGY_PARAMS[strategy].MIN_SCORE) {
            // 开多仓
            position = 'LONG';
            entryPrice = currentKline.close;
            entryTime = currentKline.timestamp;
            stopLoss = signal.stopLoss || 0;
            takeProfit = signal.takeProfit || 0;
            
            trades.push({
              id: generateId(),
              timestamp: currentKline.timestamp,
              type: 'BUY',
              price: entryPrice,
              quantity: currentCapital / entryPrice,
              reason: `入场信号 (评分: ${signal.score})`
            });
          } else if (signal.signalType === SignalType.SELL && signal.score >= STRATEGY_PARAMS[strategy].MIN_SCORE) {
            // 开空仓
            position = 'SHORT';
            entryPrice = currentKline.close;
            entryTime = currentKline.timestamp;
            stopLoss = signal.stopLoss || 0;
            takeProfit = signal.takeProfit || 0;
            
            trades.push({
              id: generateId(),
              timestamp: currentKline.timestamp,
              type: 'SELL',
              price: entryPrice,
              quantity: currentCapital / entryPrice,
              reason: `入场信号 (评分: ${signal.score})`
            });
          }
        } else {
          // 有持仓时检查出场条件
          const shouldExit = this.checkExitConditions(
            position,
            currentKline,
            entryPrice,
            stopLoss,
            takeProfit
          );

          if (shouldExit.exit) {
            // 平仓
            const exitPrice = currentKline.close;
            const quantity = currentCapital / entryPrice;
            
            let pnl = 0;
            if (position === 'LONG') {
              pnl = (exitPrice - entryPrice) * quantity;
            } else {
              pnl = (entryPrice - exitPrice) * quantity;
            }
            
            currentCapital += pnl;
            
            trades.push({
              id: generateId(),
              timestamp: currentKline.timestamp,
              type: position === 'LONG' ? 'SELL' : 'BUY',
              price: exitPrice,
              quantity,
              pnl,
              reason: shouldExit.reason
            });
            
            position = 'NONE';
          }
        }
      } catch (error) {
        this.logger.warn(`回测第${i}根K线时出错:`, error.message);
      }

      // 记录资金曲线
      equityCurve.push({
        timestamp: currentKline.timestamp,
        equity: currentCapital
      });
    }

    // 计算绩效指标
    const performance = this.calculatePerformance(trades, initialCapital, currentCapital);

    const result: BacktestResult = {
      id: generateId(),
      request,
      trades,
      performance,
      equityCurve,
      createdAt: Date.now(),
      completedAt: Date.now()
    };

    this.logger.log(`回测完成: 总收益 ${performance.totalReturnPercent.toFixed(2)}%`);
    return result;
  }

  /**
   * 检查出场条件
   */
  private checkExitConditions(
    position: 'LONG' | 'SHORT',
    currentKline: Kline,
    entryPrice: number,
    stopLoss: number,
    takeProfit: number
  ): { exit: boolean; reason: string } {
    if (position === 'LONG') {
      // 多仓止损
      if (stopLoss > 0 && currentKline.low <= stopLoss) {
        return { exit: true, reason: '触发止损' };
      }
      // 多仓止盈
      if (takeProfit > 0 && currentKline.high >= takeProfit) {
        return { exit: true, reason: '触发止盈' };
      }
    } else if (position === 'SHORT') {
      // 空仓止损
      if (stopLoss > 0 && currentKline.high >= stopLoss) {
        return { exit: true, reason: '触发止损' };
      }
      // 空仓止盈
      if (takeProfit > 0 && currentKline.low <= takeProfit) {
        return { exit: true, reason: '触发止盈' };
      }
    }

    return { exit: false, reason: '' };
  }

  /**
   * 计算绩效指标
   */
  private calculatePerformance(
    trades: BacktestTrade[],
    initialCapital: number,
    finalCapital: number
  ) {
    const totalReturn = finalCapital - initialCapital;
    const totalReturnPercent = (totalReturn / initialCapital) * 100;
    
    const completedTrades = trades.filter(t => t.pnl !== undefined);
    const winningTrades = completedTrades.filter(t => t.pnl! > 0);
    const losingTrades = completedTrades.filter(t => t.pnl! < 0);
    
    const winRate = completedTrades.length > 0 ? (winningTrades.length / completedTrades.length) * 100 : 0;
    
    const grossProfit = winningTrades.reduce((sum, t) => sum + t.pnl!, 0);
    const grossLoss = Math.abs(losingTrades.reduce((sum, t) => sum + t.pnl!, 0));
    const profitFactor = grossLoss > 0 ? grossProfit / grossLoss : grossProfit > 0 ? 999 : 0;
    
    // 计算最大回撤
    let maxDrawdown = 0;
    let peak = initialCapital;
    
    for (const trade of completedTrades) {
      const currentCapital = initialCapital + completedTrades
        .slice(0, completedTrades.indexOf(trade) + 1)
        .reduce((sum, t) => sum + t.pnl!, 0);
      
      if (currentCapital > peak) {
        peak = currentCapital;
      }
      
      const drawdown = (peak - currentCapital) / peak * 100;
      if (drawdown > maxDrawdown) {
        maxDrawdown = drawdown;
      }
    }

    return {
      totalReturn,
      totalReturnPercent,
      maxDrawdown,
      winRate,
      profitFactor,
      sharpeRatio: 0, // 简化版本暂不计算
      totalTrades: completedTrades.length,
      winningTrades: winningTrades.length,
      losingTrades: losingTrades.length
    };
  }
}
