import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';

@Entity('grid_trading_configs')
@Index(['symbol'], { unique: true })
export class GridTradingConfig {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 20 })
  symbol: string;

  @Column({ type: 'decimal', precision: 10, scale: 4 })
  gridSize: number;

  @Column({ type: 'decimal', precision: 18, scale: 8 })
  initialCapital: number;

  @Column({ type: 'decimal', precision: 18, scale: 8, nullable: true })
  basePrice: number;

  @Column({ type: 'decimal', precision: 10, scale: 4, default: 1.0 })
  minGridSize: number;

  @Column({ type: 'decimal', precision: 10, scale: 4, default: 4.0 })
  maxGridSize: number;

  @Column({ type: 'decimal', precision: 10, scale: 4, default: 0.9 })
  maxPositionRatio: number;

  @Column({ type: 'decimal', precision: 10, scale: 4, default: 0.1 })
  minPositionRatio: number;

  @Column({ type: 'boolean', default: true })
  enableVolatilityAdjustment: boolean;

  @Column({ type: 'boolean', default: true })
  enableRiskManagement: boolean;

  @Column({ type: 'boolean', default: false })
  enableFundManagement: boolean;

  @Column({ type: 'int', default: 52 })
  volatilityWindow: number;

  @Column({ type: 'decimal', precision: 5, scale: 4, default: 0.94 })
  ewmaLambda: number;

  @Column({ type: 'decimal', precision: 5, scale: 4, default: 0.7 })
  hybridWeight: number;

  @Column({ type: 'int', default: 3600 })
  adjustmentInterval: number;

  @Column({ type: 'decimal', precision: 18, scale: 8, default: 20.0 })
  minTradeAmount: number;

  @Column({ type: 'int', default: 5 })
  maxRetries: number;

  @Column({ type: 'int', default: 300 })
  riskCheckInterval: number;

  @Column({ type: 'boolean', default: true })
  isActive: boolean;

  @Column({ type: 'json', nullable: true })
  additionalParams: Record<string, any>;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
