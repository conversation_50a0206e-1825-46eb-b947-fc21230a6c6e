import {
  Controller,
  Post,
  Get,
  Put,
  Delete,
  Body,
  Param,
  Query,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { GridTradingService } from './grid-trading.service';
import { StartGridTradingDto } from './dto/start-grid-trading.dto';
import { UpdateGridConfigDto } from './dto/update-grid-config.dto';
import { GridTradingStatus } from './interfaces/grid-trading.interface';

@Controller('grid-trading')
export class GridTradingController {
  constructor(private readonly gridTradingService: GridTradingService) {}

  @Post('start')
  async startGridTrading(@Body() startDto: StartGridTradingDto) {
    try {
      const result = await this.gridTradingService.startGridTrading(startDto);
      return {
        success: true,
        data: result,
        message: '网格交易启动成功',
      };
    } catch (error) {
      throw new HttpException(
        `启动网格交易失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('stop/:symbol')
  async stopGridTrading(@Param('symbol') symbol: string) {
    try {
      const result = await this.gridTradingService.stopGridTrading(symbol);
      return {
        success: true,
        data: result,
        message: '网格交易停止成功',
      };
    } catch (error) {
      throw new HttpException(
        `停止网格交易失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('status')
  async getAllTradingStatus() {
    try {
      const statuses = await this.gridTradingService.getAllTradingStatus();
      return {
        success: true,
        data: statuses,
      };
    } catch (error) {
      throw new HttpException(
        `获取交易状态失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('status/:symbol')
  async getTradingStatus(@Param('symbol') symbol: string) {
    try {
      const status = await this.gridTradingService.getTradingStatus(symbol);
      return {
        success: true,
        data: status,
      };
    } catch (error) {
      throw new HttpException(
        `获取交易状态失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Put('config/:symbol')
  async updateGridConfig(
    @Param('symbol') symbol: string,
    @Body() updateDto: UpdateGridConfigDto,
  ) {
    try {
      const result = await this.gridTradingService.updateGridConfig(
        symbol,
        updateDto,
      );
      return {
        success: true,
        data: result,
        message: '网格配置更新成功',
      };
    } catch (error) {
      throw new HttpException(
        `更新网格配置失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('positions/:symbol')
  async getTradingPositions(
    @Param('symbol') symbol: string,
    @Query('limit') limit?: number,
  ) {
    try {
      const positions = await this.gridTradingService.getTradingPositions(
        symbol,
        limit,
      );
      return {
        success: true,
        data: positions,
      };
    } catch (error) {
      throw new HttpException(
        `获取交易仓位失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('orders/:symbol')
  async getOrderHistory(
    @Param('symbol') symbol: string,
    @Query('limit') limit?: number,
  ) {
    try {
      const orders = await this.gridTradingService.getOrderHistory(
        symbol,
        limit,
      );
      return {
        success: true,
        data: orders,
      };
    } catch (error) {
      throw new HttpException(
        `获取订单历史失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('performance/:symbol')
  async getTradingPerformance(@Param('symbol') symbol: string) {
    try {
      const performance = await this.gridTradingService.getTradingPerformance(
        symbol,
      );
      return {
        success: true,
        data: performance,
      };
    } catch (error) {
      throw new HttpException(
        `获取交易表现失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('emergency-stop')
  async emergencyStopAll() {
    try {
      const result = await this.gridTradingService.emergencyStopAll();
      return {
        success: true,
        data: result,
        message: '紧急停止所有交易成功',
      };
    } catch (error) {
      throw new HttpException(
        `紧急停止失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
