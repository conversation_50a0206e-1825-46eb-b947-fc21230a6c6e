import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BullModule } from '@nestjs/bull';
import { BacktestController } from './backtest.controller';
import { BacktestService } from './backtest.service';
import { BacktestProcessor } from './backtest.processor';
import { BacktestEngineService } from './backtest-engine.service';
import { BacktestJobEntity } from '../../entities/backtest-job.entity';
import { MarketDataModule } from '../market-data/market-data.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([BacktestJobEntity]),
    BullModule.registerQueue({
      name: 'backtest',
    }),
    MarketDataModule,
  ],
  controllers: [BacktestController],
  providers: [
    BacktestService,
    BacktestProcessor,
    BacktestEngineService,
  ],
  exports: [BacktestService],
})
export class BacktestModule {}
