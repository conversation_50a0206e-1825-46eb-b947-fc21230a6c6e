import { Injectable, Logger } from '@nestjs/common';
import { Kline, TradingPair, TimeFrame, SUPPORTED_PAIRS } from '@signal-dash/shared';
import { BinanceApiService } from './binance-api.service';

/**
 * 市场数据缓存接口
 */
interface MarketDataCache {
  [key: string]: {
    data: Kline[];
    lastUpdate: number;
    ttl: number; // 缓存生存时间(毫秒)
  };
}

@Injectable()
export class MarketDataService {
  private readonly logger = new Logger(MarketDataService.name);
  private readonly cache: MarketDataCache = {};
  
  // 缓存TTL配置 (毫秒)
  private readonly cacheTTL = {
    '15m': 5 * 60 * 1000,    // 15分钟数据缓存5分钟
    '1h': 15 * 60 * 1000,    // 1小时数据缓存15分钟
    '4h': 30 * 60 * 1000,    // 4小时数据缓存30分钟
    '1d': 60 * 60 * 1000,    // 1天数据缓存1小时
  };
  
  constructor(private binanceApi: BinanceApiService) {}
  
  /**
   * 获取K线数据 (带缓存)
   */
  async getKlines(
    pair: TradingPair,
    timeFrame: TimeFrame,
    limit: number = 500,
    useCache: boolean = true
  ): Promise<Kline[]> {
    const cacheKey = this.getCacheKey(pair, timeFrame, limit);
    
    // 检查缓存
    if (useCache && this.isCacheValid(cacheKey)) {
      this.logger.debug(`从缓存获取数据: ${cacheKey}`);
      return this.cache[cacheKey].data;
    }
    
    try {
      // 从API获取数据
      this.logger.debug(`从API获取数据: ${pair} ${timeFrame} limit=${limit}`);
      const klines = await this.binanceApi.getKlines(pair, timeFrame, limit);
      
      // 更新缓存
      if (useCache) {
        this.updateCache(cacheKey, klines, timeFrame);
      }
      
      return klines;
    } catch (error) {
      this.logger.error(`获取K线数据失败: ${pair} ${timeFrame}`, error);
      
      // 如果API失败，尝试返回过期的缓存数据
      if (this.cache[cacheKey]) {
        this.logger.warn(`API失败，返回过期缓存数据: ${cacheKey}`);
        return this.cache[cacheKey].data;
      }
      
      throw error;
    }
  }
  
  /**
   * 获取历史数据 (用于回测)
   */
  async getHistoricalData(
    pair: TradingPair,
    timeFrame: TimeFrame,
    startTime: number,
    endTime: number
  ): Promise<Kline[]> {
    const allKlines: Kline[] = [];
    const maxLimit = 1000;
    let currentStartTime = startTime;
    
    this.logger.debug(`获取历史数据: ${pair} ${timeFrame} ${new Date(startTime)} - ${new Date(endTime)}`);
    
    while (currentStartTime < endTime) {
      try {
        const klines = await this.binanceApi.getKlines(
          pair,
          timeFrame,
          maxLimit,
          currentStartTime,
          endTime
        );
        
        if (klines.length === 0) {
          break;
        }
        
        allKlines.push(...klines);
        
        // 更新下次请求的开始时间
        const lastTimestamp = klines[klines.length - 1].timestamp;
        currentStartTime = lastTimestamp + this.getTimeFrameMs(timeFrame);
        
        // 避免请求过于频繁
        await this.delay(100);
      } catch (error) {
        this.logger.error(`获取历史数据失败: ${currentStartTime}`, error);
        break;
      }
    }
    
    // 去重并排序
    const uniqueKlines = this.deduplicateKlines(allKlines);
    this.logger.debug(`获取到 ${uniqueKlines.length} 条历史数据`);
    
    return uniqueKlines;
  }
  
  /**
   * 获取所有支持交易对的最新价格
   */
  async getAllLatestPrices(): Promise<{ [pair: string]: number }> {
    const prices: { [pair: string]: number } = {};
    
    for (const pair of SUPPORTED_PAIRS) {
      try {
        prices[pair] = await this.binanceApi.getLatestPrice(pair);
      } catch (error) {
        this.logger.error(`获取${pair}价格失败`, error);
      }
    }
    
    return prices;
  }
  
  /**
   * 预热缓存 (启动时调用)
   */
  async warmupCache(): Promise<void> {
    this.logger.log('开始预热市场数据缓存...');
    
    const timeFrames: TimeFrame[] = [TimeFrame.M15, TimeFrame.H1, TimeFrame.H4, TimeFrame.D1];
    
    for (const pair of SUPPORTED_PAIRS) {
      for (const timeFrame of timeFrames) {
        try {
          await this.getKlines(pair, timeFrame, 200, true);
          await this.delay(200); // 避免请求过于频繁
        } catch (error) {
          this.logger.warn(`预热缓存失败: ${pair} ${timeFrame}`, error);
        }
      }
    }
    
    this.logger.log('市场数据缓存预热完成');
  }
  
  /**
   * 清理过期缓存
   */
  cleanExpiredCache(): void {
    const now = Date.now();
    let cleanedCount = 0;
    
    for (const [key, cache] of Object.entries(this.cache)) {
      if (now - cache.lastUpdate > cache.ttl) {
        delete this.cache[key];
        cleanedCount++;
      }
    }
    
    if (cleanedCount > 0) {
      this.logger.debug(`清理了 ${cleanedCount} 个过期缓存项`);
    }
  }
  
  /**
   * 生成缓存键
   */
  private getCacheKey(pair: TradingPair, timeFrame: TimeFrame, limit: number): string {
    return `${pair}_${timeFrame}_${limit}`;
  }
  
  /**
   * 检查缓存是否有效
   */
  private isCacheValid(cacheKey: string): boolean {
    const cache = this.cache[cacheKey];
    if (!cache) return false;
    
    const now = Date.now();
    return (now - cache.lastUpdate) < cache.ttl;
  }
  
  /**
   * 更新缓存
   */
  private updateCache(cacheKey: string, data: Kline[], timeFrame: TimeFrame): void {
    this.cache[cacheKey] = {
      data,
      lastUpdate: Date.now(),
      ttl: this.cacheTTL[timeFrame] || 5 * 60 * 1000,
    };
  }
  
  /**
   * 获取时间框架对应的毫秒数
   */
  private getTimeFrameMs(timeFrame: TimeFrame): number {
    const timeFrameMs = {
      '15m': 15 * 60 * 1000,
      '1h': 60 * 60 * 1000,
      '4h': 4 * 60 * 60 * 1000,
      '1d': 24 * 60 * 60 * 1000,
    };
    
    return timeFrameMs[timeFrame] || 60 * 60 * 1000;
  }
  
  /**
   * K线数据去重
   */
  private deduplicateKlines(klines: Kline[]): Kline[] {
    const seen = new Set<number>();
    return klines
      .filter(kline => {
        if (seen.has(kline.timestamp)) {
          return false;
        }
        seen.add(kline.timestamp);
        return true;
      })
      .sort((a, b) => a.timestamp - b.timestamp);
  }
  
  /**
   * 延迟函数
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
