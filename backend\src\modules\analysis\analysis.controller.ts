import { Controller, Post, Body, HttpException, HttpStatus } from '@nestjs/common';
import { AnalysisService } from './analysis.service';
import { AnalyzeRequestDto } from './dto/analyze-request.dto';
import { AnalyzeResponse } from '@signal-dash/shared';

@Controller('analyze-now')
export class AnalysisController {
  constructor(private readonly analysisService: AnalysisService) {}

  @Post()
  async analyzeNow(@Body() request: AnalyzeRequestDto): Promise<AnalyzeResponse> {
    try {
      return await this.analysisService.analyzeNow(request.asset, request.strategy);
    } catch (error) {
      throw new HttpException(
        `分析失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }
}
