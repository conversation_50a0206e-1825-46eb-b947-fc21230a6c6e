import axios from 'axios';
import { 
  AnalyzeRequest, 
  AnalyzeResponse, 
  BacktestRequest, 
  BacktestJob, 
  BacktestResult,
  TradingSignal,
  SystemConfig
} from '@signal-dash/shared';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3003/api';

const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
apiClient.interceptors.request.use(
  (config) => {
    console.log(`API请求: ${config.method?.toUpperCase()} ${config.url}`);
    return config;
  },
  (error) => Promise.reject(error)
);

// 响应拦截器
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('API请求失败:', error.response?.data || error.message);
    return Promise.reject(error);
  }
);

export const api = {
  // 健康检查
  health: () => apiClient.get('/health'),

  // 手动分析
  analyzeNow: (request: AnalyzeRequest): Promise<AnalyzeResponse> =>
    apiClient.post('/analyze-now', request).then(res => res.data),

  // 回测相关
  backtest: {
    start: (request: BacktestRequest): Promise<{ jobId: string }> =>
      apiClient.post('/backtest', request).then(res => res.data),
    
    getStatus: (jobId: string): Promise<BacktestJob> =>
      apiClient.get(`/backtest/${jobId}/status`).then(res => res.data),
    
    getResults: (jobId: string): Promise<BacktestResult> =>
      apiClient.get(`/backtest/${jobId}/results`).then(res => res.data),
  },

  // 信号相关
  signals: {
    getList: (params?: {
      asset?: string;
      strategy?: string;
      limit?: number;
      offset?: number;
    }): Promise<{ signals: TradingSignal[]; total: number }> =>
      apiClient.get('/signals', { params }).then(res => res.data),
    
    getById: (id: string): Promise<TradingSignal> =>
      apiClient.get(`/signals/${id}`).then(res => res.data),
    
    delete: (id: string): Promise<{ success: boolean }> =>
      apiClient.delete(`/signals/${id}`).then(res => res.data),
    
    getStats: (): Promise<{
      totalSignals: number;
      todaySignals: number;
      signalsByAsset: { [asset: string]: number };
      signalsByStrategy: { [strategy: string]: number };
    }> =>
      apiClient.get('/signals/stats/summary').then(res => res.data),
  },

  // 配置相关
  config: {
    get: (): Promise<SystemConfig> =>
      apiClient.get('/config').then(res => res.data),
    
    update: (config: Partial<SystemConfig>): Promise<{ success: boolean }> =>
      apiClient.put('/config', config).then(res => res.data),
    
    testNotification: (): Promise<{ success: boolean; message: string }> =>
      apiClient.post('/config/test-notification').then(res => res.data),
    
    getSystemStatus: (): Promise<{
      database: boolean;
      binanceApi: boolean;
      redis: boolean;
      notification: boolean;
    }> =>
      apiClient.get('/config/system-status').then(res => res.data),
  },
};
