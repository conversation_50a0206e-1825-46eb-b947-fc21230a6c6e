import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Decimal } from 'decimal.js';
import { VolatilityData } from '../entities/volatility-data.entity';
import { VolatilityMetrics } from '../interfaces/grid-trading.interface';

export interface KlineData {
  timestamp: number;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

@Injectable()
export class VolatilityCalculatorService {
  private readonly logger = new Logger(VolatilityCalculatorService.name);

  // EWMA状态缓存，按交易对存储
  private ewmaStates = new Map<string, {
    volatility: number;
    lastPrice: number;
    initialized: boolean;
  }>();

  // 波动率历史缓存，用于平滑化
  private volatilityHistory = new Map<string, number[]>();

  constructor(
    @InjectRepository(VolatilityData)
    private readonly volatilityRepository: Repository<VolatilityData>,
  ) {}

  /**
   * 计算综合波动率指标
   */
  async calculateVolatilityMetrics(
    symbol: string,
    klines: KlineData[],
    config: {
      window?: number;
      ewmaLambda?: number;
      hybridWeight?: number;
      enableVolumeWeighting?: boolean;
      smoothingWindow?: number;
    } = {},
  ): Promise<VolatilityMetrics> {
    const {
      window = 52,
      ewmaLambda = 0.94,
      hybridWeight = 0.7,
      enableVolumeWeighting = true,
      smoothingWindow = 3,
    } = config;

    if (klines.length < 2) {
      throw new Error('至少需要2个K线数据点来计算波动率');
    }

    const currentPrice = klines[klines.length - 1].close;
    const timestamp = new Date(klines[klines.length - 1].timestamp);

    // 1. 计算传统波动率
    const traditionalVolatility = this.calculateTraditionalVolatility(
      klines,
      window,
      enableVolumeWeighting,
    );

    // 2. 计算EWMA波动率
    const ewmaVolatility = this.calculateEWMAVolatility(
      symbol,
      currentPrice,
      ewmaLambda,
    );

    // 3. 计算混合波动率
    const hybridVolatility = this.calculateHybridVolatility(
      traditionalVolatility,
      ewmaVolatility,
      hybridWeight,
    );

    // 4. 年化波动率
    const annualizedVolatility = this.annualizeVolatility(hybridVolatility);

    // 5. 成交量加权波动率（可选）
    let volumeWeightedVolatility: number | undefined;
    if (enableVolumeWeighting) {
      volumeWeightedVolatility = this.calculateVolumeWeightedVolatility(klines);
    }

    // 6. 平滑化波动率
    const smoothedVolatility = this.applySmoothingFilter(
      symbol,
      annualizedVolatility,
      smoothingWindow,
    );

    const metrics: VolatilityMetrics = {
      traditionalVolatility,
      ewmaVolatility,
      hybridVolatility,
      annualizedVolatility,
      volumeWeightedVolatility,
      smoothedVolatility,
      timestamp,
    };

    // 保存到数据库
    await this.saveVolatilityData(symbol, klines[klines.length - 1], metrics, window);

    return metrics;
  }

  /**
   * 计算传统波动率（基于对数收益率标准差）
   */
  private calculateTraditionalVolatility(
    klines: KlineData[],
    window: number,
    enableVolumeWeighting: boolean,
  ): number {
    if (klines.length < 2) return 0.2; // 默认值

    const prices = klines.map(k => k.close);
    const volumes = klines.map(k => k.volume);

    // 计算对数收益率
    const logReturns: number[] = [];
    for (let i = 1; i < prices.length; i++) {
      const logReturn = Math.log(prices[i] / prices[i - 1]);
      logReturns.push(logReturn);
    }

    if (!enableVolumeWeighting) {
      // 简单标准差计算
      const mean = logReturns.reduce((sum, r) => sum + r, 0) / logReturns.length;
      const variance = logReturns.reduce((sum, r) => sum + Math.pow(r - mean, 2), 0) / logReturns.length;
      return Math.sqrt(variance);
    }

    // 成交量加权计算
    const relevantVolumes = volumes.slice(1); // 对应收益率的成交量
    const averageVolume = relevantVolumes.reduce((sum, v) => sum + v, 0) / relevantVolumes.length;

    if (averageVolume === 0) {
      // 退回到简单计算
      const mean = logReturns.reduce((sum, r) => sum + r, 0) / logReturns.length;
      const variance = logReturns.reduce((sum, r) => sum + Math.pow(r - mean, 2), 0) / logReturns.length;
      return Math.sqrt(variance);
    }

    // 计算成交量权重
    const volumeWeights = relevantVolumes.map(v => v / averageVolume);

    // 加权收益率
    const weightedReturns = logReturns.map((r, i) => r * volumeWeights[i]);

    // 计算加权标准差
    const weightedMean = weightedReturns.reduce((sum, r) => sum + r, 0) / weightedReturns.length;
    const weightedVariance = weightedReturns.reduce(
      (sum, r) => sum + Math.pow(r - weightedMean, 2),
      0,
    ) / weightedReturns.length;

    return Math.sqrt(weightedVariance);
  }

  /**
   * 计算EWMA波动率
   */
  private calculateEWMAVolatility(
    symbol: string,
    currentPrice: number,
    lambda: number,
  ): number {
    let state = this.ewmaStates.get(symbol);

    if (!state) {
      // 初始化状态
      state = {
        volatility: 0,
        lastPrice: currentPrice,
        initialized: false,
      };
      this.ewmaStates.set(symbol, state);
      return 0.2; // 默认初始波动率
    }

    if (!state.initialized) {
      // 首次计算
      if (state.lastPrice > 0) {
        const returnSquared = Math.pow(Math.log(currentPrice / state.lastPrice), 2);
        state.volatility = returnSquared;
        state.initialized = true;
      }
      state.lastPrice = currentPrice;
      return Math.sqrt(state.volatility);
    }

    // EWMA更新公式：σ²(t) = λ * σ²(t-1) + (1-λ) * r²(t)
    const returnSquared = Math.pow(Math.log(currentPrice / state.lastPrice), 2);
    state.volatility = lambda * state.volatility + (1 - lambda) * returnSquared;
    state.lastPrice = currentPrice;

    return Math.sqrt(state.volatility);
  }

  /**
   * 计算混合波动率
   */
  private calculateHybridVolatility(
    traditionalVol: number,
    ewmaVol: number,
    hybridWeight: number,
  ): number {
    return hybridWeight * ewmaVol + (1 - hybridWeight) * traditionalVol;
  }

  /**
   * 年化波动率
   */
  private annualizeVolatility(volatility: number): number {
    // 假设使用4小时K线，一年有365 * 6 = 2190个4小时周期
    return volatility * Math.sqrt(365 * 6);
  }

  /**
   * 计算成交量加权波动率
   */
  private calculateVolumeWeightedVolatility(klines: KlineData[]): number {
    if (klines.length < 2) return 0.2;

    const prices = klines.map(k => k.close);
    const volumes = klines.map(k => k.volume);

    // 计算价格变化率
    const priceChanges: number[] = [];
    for (let i = 1; i < prices.length; i++) {
      priceChanges.push((prices[i] - prices[i - 1]) / prices[i - 1]);
    }

    // 对应的成交量
    const relevantVolumes = volumes.slice(1);
    const totalVolume = relevantVolumes.reduce((sum, v) => sum + v, 0);

    if (totalVolume === 0) return 0.2;

    // 成交量加权方差
    let weightedVariance = 0;
    for (let i = 0; i < priceChanges.length; i++) {
      const weight = relevantVolumes[i] / totalVolume;
      weightedVariance += weight * Math.pow(priceChanges[i], 2);
    }

    return Math.sqrt(weightedVariance);
  }

  /**
   * 应用平滑化滤波器
   */
  private applySmoothingFilter(
    symbol: string,
    currentVolatility: number,
    windowSize: number,
  ): number {
    let history = this.volatilityHistory.get(symbol) || [];
    
    // 添加当前值
    history.push(currentVolatility);
    
    // 保持窗口大小
    if (history.length > windowSize) {
      history = history.slice(-windowSize);
    }
    
    // 更新缓存
    this.volatilityHistory.set(symbol, history);
    
    // 计算移动平均
    const average = history.reduce((sum, vol) => sum + vol, 0) / history.length;
    return average;
  }

  /**
   * 保存波动率数据到数据库
   */
  private async saveVolatilityData(
    symbol: string,
    kline: KlineData,
    metrics: VolatilityMetrics,
    windowSize: number,
  ): Promise<void> {
    try {
      const volatilityData = this.volatilityRepository.create({
        symbol,
        timestamp: new Date(kline.timestamp),
        price: kline.close,
        volume: kline.volume,
        logReturn: kline.close > 0 ? Math.log(kline.close / kline.open) : 0,
        traditionalVolatility: metrics.traditionalVolatility,
        ewmaVolatility: metrics.ewmaVolatility,
        hybridVolatility: metrics.hybridVolatility,
        annualizedVolatility: metrics.annualizedVolatility,
        volumeWeightedVolatility: metrics.volumeWeightedVolatility,
        smoothedVolatility: metrics.smoothedVolatility,
        windowSize,
        metadata: {
          calculatedAt: new Date(),
          klineCount: windowSize,
        },
      });

      await this.volatilityRepository.save(volatilityData);
    } catch (error) {
      this.logger.error(`保存波动率数据失败: ${error.message}`, error.stack);
    }
  }

  /**
   * 获取历史波动率数据
   */
  async getHistoricalVolatility(
    symbol: string,
    limit: number = 100,
  ): Promise<VolatilityData[]> {
    return this.volatilityRepository.find({
      where: { symbol },
      order: { timestamp: 'DESC' },
      take: limit,
    });
  }

  /**
   * 清理EWMA状态（用于重置）
   */
  clearEWMAState(symbol: string): void {
    this.ewmaStates.delete(symbol);
    this.volatilityHistory.delete(symbol);
    this.logger.log(`已清理 ${symbol} 的EWMA状态`);
  }
}
