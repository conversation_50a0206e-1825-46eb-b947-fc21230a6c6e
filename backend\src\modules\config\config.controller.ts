import { Controller, Get } from '@nestjs/common';

@Controller('config')
export class ConfigController {
  @Get()
  async getConfig() {
    // 返回符合SystemConfig接口的模拟配置数据
    return {
      ntfy: {
        url: 'https://ntfy.sh',
        topic: 'signal-dash-demo',
        enabled: true,
      },
      binance: {
        apiUrl: 'https://api.binance.com',
        testnet: false,
      },
      monitoring: {
        enabled: true,
        interval: 5, // 5分钟
        assets: ['BTC/USDT', 'ETH/USDT', 'SOL/USDT'],
      },
    };
  }

  @Get('system-status')
  async getSystemStatus() {
    // 返回模拟系统状态
    return {
      database: true, // 数据库状态正常（演示模式）
      binanceApi: true,
      redis: true,
      notification: true,
    };
  }
}
