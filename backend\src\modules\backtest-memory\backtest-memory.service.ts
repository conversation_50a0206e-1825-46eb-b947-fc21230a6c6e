import { Injectable } from '@nestjs/common';

@Injectable()
export class BacktestMemoryService {
  private backtests: Map<string, any> = new Map();

  async createBacktest(config: any) {
    const id = this.generateId();
    const backtest = {
      id,
      ...config,
      status: 'running',
      progress: 0,
      createdAt: new Date(),
    };
    
    this.backtests.set(id, backtest);
    
    // 模拟异步回测过程
    this.simulateBacktest(id);
    
    return backtest;
  }

  async getBacktest(id: string) {
    return this.backtests.get(id) || null;
  }

  async getBacktests() {
    return Array.from(this.backtests.values());
  }

  async deleteBacktest(id: string) {
    return this.backtests.delete(id);
  }

  private async simulateBacktest(id: string) {
    const backtest = this.backtests.get(id);
    if (!backtest) return;

    // 模拟进度更新
    for (let progress = 0; progress <= 100; progress += 10) {
      await new Promise(resolve => setTimeout(resolve, 500));
      backtest.progress = progress;
      
      if (progress === 100) {
        backtest.status = 'completed';
        backtest.result = {
          totalTrades: 150,
          winRate: 65.2,
          totalReturn: 23.5,
          maxDrawdown: -8.2,
          sharpeRatio: 1.45,
          trades: this.generateMockTrades(),
          equity: this.generateMockEquity(),
        };
        backtest.completedAt = new Date();
      }
    }
  }

  private generateMockTrades() {
    const trades = [];
    for (let i = 0; i < 20; i++) {
      trades.push({
        id: `trade_${i}`,
        timestamp: new Date(Date.now() - i * 3600000),
        type: Math.random() > 0.5 ? 'BUY' : 'SELL',
        price: 45000 + Math.random() * 10000,
        quantity: Math.random() * 0.1,
        pnl: (Math.random() - 0.5) * 1000,
      });
    }
    return trades;
  }

  private generateMockEquity() {
    const equity = [];
    let value = 10000;
    for (let i = 0; i < 100; i++) {
      value += (Math.random() - 0.48) * 100; // 略微向上趋势
      equity.push({
        timestamp: new Date(Date.now() - (100 - i) * 3600000),
        value: Math.max(value, 5000), // 最低5000
      });
    }
    return equity;
  }

  private generateId(): string {
    return Math.random().toString(36).substr(2, 9);
  }
}
