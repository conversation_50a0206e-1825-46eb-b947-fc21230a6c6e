import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';
import { TradingSignal, SignalType } from '@signal-dash/shared';

@Injectable()
export class NotificationService {
  private readonly logger = new Logger(NotificationService.name);
  private readonly ntfyUrl: string;
  private readonly ntfyTopic: string;
  private readonly enabled: boolean;

  constructor(private configService: ConfigService) {
    this.ntfyUrl = this.configService.get<string>('NTFY_URL', 'https://ntfy.sh');
    this.ntfyTopic = this.configService.get<string>('NTFY_TOPIC', 'signal-dash-pro');
    this.enabled = this.configService.get<boolean>('NTFY_ENABLED', false);
  }

  /**
   * 发送信号通知
   */
  async sendSignalNotification(signal: TradingSignal): Promise<void> {
    if (!this.enabled || !this.ntfyTopic) {
      this.logger.debug('通知功能未启用或未配置主题');
      return;
    }

    try {
      const message = this.formatSignalMessage(signal);
      const title = this.formatSignalTitle(signal);
      
      await this.sendNotification(title, message, this.getSignalPriority(signal));
      
      this.logger.log(`信号通知已发送: ${signal.asset} ${signal.signalType}`);
    } catch (error) {
      this.logger.error('发送信号通知失败:', error);
    }
  }

  /**
   * 发送系统通知
   */
  async sendSystemNotification(title: string, message: string, priority: 'low' | 'default' | 'high' = 'default'): Promise<void> {
    if (!this.enabled || !this.ntfyTopic) {
      return;
    }

    try {
      await this.sendNotification(title, message, priority);
      this.logger.log(`系统通知已发送: ${title}`);
    } catch (error) {
      this.logger.error('发送系统通知失败:', error);
    }
  }

  /**
   * 发送回测完成通知
   */
  async sendBacktestCompletedNotification(
    asset: string,
    strategy: string,
    totalReturn: number,
    winRate: number
  ): Promise<void> {
    if (!this.enabled || !this.ntfyTopic) {
      return;
    }

    try {
      const title = '📊 回测完成';
      const message = `${asset} ${strategy}策略回测完成\n` +
                     `总收益: ${totalReturn.toFixed(2)}%\n` +
                     `胜率: ${winRate.toFixed(1)}%`;
      
      await this.sendNotification(title, message, 'default');
      this.logger.log(`回测完成通知已发送: ${asset} ${strategy}`);
    } catch (error) {
      this.logger.error('发送回测完成通知失败:', error);
    }
  }

  /**
   * 发送通知到ntfy
   */
  private async sendNotification(title: string, message: string, priority: 'low' | 'default' | 'high'): Promise<void> {
    const url = `${this.ntfyUrl}/${this.ntfyTopic}`;
    
    const headers: any = {
      'Title': title,
      'Priority': priority,
      'Tags': 'chart_with_upwards_trend',
    };

    // 根据优先级设置不同的标签
    if (priority === 'high') {
      headers['Tags'] = 'warning,chart_with_upwards_trend';
    } else if (priority === 'low') {
      headers['Tags'] = 'information_source';
    }

    await axios.post(url, message, {
      headers,
      timeout: 5000,
    });
  }

  /**
   * 格式化信号标题
   */
  private formatSignalTitle(signal: TradingSignal): string {
    const emoji = signal.signalType === SignalType.BUY ? '🟢' : '🔴';
    const action = signal.signalType === SignalType.BUY ? '买入' : '卖出';
    return `${emoji} ${signal.asset} ${action}信号`;
  }

  /**
   * 格式化信号消息
   */
  private formatSignalMessage(signal: TradingSignal): string {
    const strategyName = signal.strategy === 'TREND_FOLLOWING' ? '趋势跟踪' : '震荡区间';
    const action = signal.signalType === SignalType.BUY ? '买入' : '卖出';
    
    let message = `策略: ${strategyName}\n`;
    message += `建议: ${action}\n`;
    message += `价格: ${signal.price.toFixed(4)}\n`;
    message += `评分: ${signal.score}/100\n`;
    
    if (signal.stopLoss) {
      message += `止损: ${signal.stopLoss.toFixed(4)}\n`;
    }
    
    if (signal.takeProfit) {
      message += `止盈: ${signal.takeProfit.toFixed(4)}\n`;
    }
    
    // 添加主要分析依据
    if (signal.reasoning.length > 0) {
      message += `\n主要依据:\n${signal.reasoning.slice(0, 3).join('\n')}`;
    }
    
    return message;
  }

  /**
   * 获取信号优先级
   */
  private getSignalPriority(signal: TradingSignal): 'low' | 'default' | 'high' {
    if (signal.score >= 85) {
      return 'high';
    } else if (signal.score >= 70) {
      return 'default';
    } else {
      return 'low';
    }
  }

  /**
   * 测试通知功能
   */
  async testNotification(): Promise<boolean> {
    try {
      await this.sendSystemNotification(
        '🧪 测试通知',
        'Signal-Dash Pro 通知功能正常工作',
        'low'
      );
      return true;
    } catch (error) {
      this.logger.error('测试通知失败:', error);
      return false;
    }
  }
}
