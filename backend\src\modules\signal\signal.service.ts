import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindManyOptions } from 'typeorm';
import { Cron, CronExpression } from '@nestjs/schedule';
import { SignalEntity } from '../../entities/signal.entity';
import { TradingSignal, TradingPair, StrategyType, TimeFrame, SUPPORTED_PAIRS } from '@signal-dash/shared';
import { MarketDataService } from '../../common/services/market-data.service';
import { StrategyService } from '../../common/services/strategy.service';
import { WebsocketGateway } from '../websocket/websocket.gateway';
import { NotificationService } from '../../common/services/notification.service';

interface SignalQueryOptions {
  asset?: TradingPair;
  strategy?: StrategyType;
  limit?: number;
  offset?: number;
}

@Injectable()
export class SignalService {
  private readonly logger = new Logger(SignalService.name);

  constructor(
    @InjectRepository(SignalEntity)
    private signalRepository: Repository<SignalEntity>,
    private marketDataService: MarketDataService,
    private strategyService: StrategyService,
    private websocketGateway: WebsocketGateway,
    private notificationService: NotificationService
  ) {}

  /**
   * 获取信号列表
   */
  async getSignals(options: SignalQueryOptions): Promise<{ signals: TradingSignal[]; total: number }> {
    const { asset, strategy, limit = 50, offset = 0 } = options;

    const queryOptions: FindManyOptions<SignalEntity> = {
      order: { timestamp: 'DESC' },
      take: limit,
      skip: offset,
    };

    const where: any = {};
    if (asset) where.asset = asset;
    if (strategy) where.strategy = strategy;
    
    if (Object.keys(where).length > 0) {
      queryOptions.where = where;
    }

    const [entities, total] = await this.signalRepository.findAndCount(queryOptions);
    
    const signals = entities.map(this.entityToSignal);
    return { signals, total };
  }

  /**
   * 根据ID获取信号
   */
  async getSignalById(id: string): Promise<TradingSignal | null> {
    const entity = await this.signalRepository.findOne({ where: { id } });
    return entity ? this.entityToSignal(entity) : null;
  }

  /**
   * 保存信号
   */
  async saveSignal(signal: TradingSignal): Promise<void> {
    const entity = this.signalRepository.create({
      id: signal.id,
      timestamp: signal.timestamp,
      asset: signal.asset,
      strategy: signal.strategy,
      timeFrame: signal.timeFrame,
      signalType: signal.signalType,
      score: signal.score,
      price: signal.price,
      stopLoss: signal.stopLoss,
      takeProfit: signal.takeProfit,
      reasoning: signal.reasoning,
      indicators: signal.indicators,
      notified: false,
    });

    await this.signalRepository.save(entity);
    this.logger.log(`信号已保存: ${signal.id} ${signal.asset} ${signal.signalType}`);

    // 通过WebSocket广播新信号
    this.websocketGateway.broadcastSignal(signal);

    // 发送推送通知
    await this.notificationService.sendSignalNotification(signal);
  }

  /**
   * 删除信号
   */
  async deleteSignal(id: string): Promise<void> {
    await this.signalRepository.delete(id);
    this.logger.log(`信号已删除: ${id}`);
  }

  /**
   * 获取信号统计
   */
  async getSignalStats(): Promise<{
    totalSignals: number;
    todaySignals: number;
    signalsByAsset: { [asset: string]: number };
    signalsByStrategy: { [strategy: string]: number };
  }> {
    const totalSignals = await this.signalRepository.count();
    
    // 今日信号数量
    const todayStart = new Date();
    todayStart.setHours(0, 0, 0, 0);
    const todaySignals = await this.signalRepository.count({
      where: {
        timestamp: todayStart.getTime()
      }
    });

    // 按资产统计
    const signalsByAsset: { [asset: string]: number } = {};
    for (const asset of SUPPORTED_PAIRS) {
      signalsByAsset[asset] = await this.signalRepository.count({ where: { asset } });
    }

    // 按策略统计
    const signalsByStrategy: { [strategy: string]: number } = {};
    for (const strategy of Object.values(StrategyType)) {
      signalsByStrategy[strategy] = await this.signalRepository.count({ where: { strategy } });
    }

    return {
      totalSignals,
      todaySignals,
      signalsByAsset,
      signalsByStrategy,
    };
  }

  /**
   * 定时扫描市场信号 (每15分钟执行一次)
   */
  @Cron('0 */15 * * * *') // 每15分钟执行一次
  async scanMarketSignals(): Promise<void> {
    this.logger.log('开始扫描市场信号...');

    try {
      for (const asset of SUPPORTED_PAIRS) {
        // 使用所有策略分析市场
        const signals = await this.analyzeAssetWithAllStrategies(asset);
        
        for (const signal of signals) {
          // 只保存高质量信号
          if (this.isHighQualitySignal(signal)) {
            await this.saveSignal(signal);
          }
        }

        // 避免请求过于频繁
        await this.delay(1000);
      }
    } catch (error) {
      this.logger.error('扫描市场信号失败:', error);
    }

    this.logger.log('市场信号扫描完成');
  }

  /**
   * 使用所有策略分析资产
   */
  private async analyzeAssetWithAllStrategies(asset: TradingPair): Promise<TradingSignal[]> {
    const signals: TradingSignal[] = [];

    for (const strategyType of Object.values(StrategyType)) {
      try {
        const timeFrame = strategyType === StrategyType.TREND_FOLLOWING ? TimeFrame.H4 : TimeFrame.M15;
        const klines = await this.marketDataService.getKlines(asset, timeFrame, 300, true);
        
        if (klines.length >= 200) {
          const signal = this.strategyService.analyzeWithStrategy(strategyType, klines, asset);
          if (signal) {
            signals.push(signal);
          }
        }
      } catch (error) {
        this.logger.warn(`分析${asset}使用${strategyType}策略失败:`, error.message);
      }
    }

    return signals;
  }

  /**
   * 判断是否为高质量信号
   */
  private isHighQualitySignal(signal: TradingSignal): boolean {
    // 只保存买入和卖出信号，忽略观望信号
    if (signal.signalType === 'HOLD') {
      return false;
    }

    // 检查评分是否达到最低要求
    const minScore = signal.strategy === StrategyType.TREND_FOLLOWING ? 60 : 70;
    return signal.score >= minScore;
  }

  /**
   * 实体转换为信号对象
   */
  private entityToSignal(entity: SignalEntity): TradingSignal {
    return {
      id: entity.id,
      timestamp: entity.timestamp,
      asset: entity.asset,
      strategy: entity.strategy,
      timeFrame: entity.timeFrame,
      signalType: entity.signalType,
      score: entity.score,
      price: entity.price,
      stopLoss: entity.stopLoss,
      takeProfit: entity.takeProfit,
      reasoning: entity.reasoning,
      indicators: entity.indicators,
    };
  }

  /**
   * 延迟函数
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
