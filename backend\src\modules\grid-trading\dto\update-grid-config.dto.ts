import {
  IsN<PERSON>ber,
  IsOptional,
  IsBoolean,
  Min,
  Max,
  IsPositive,
  IsObject,
} from 'class-validator';
import { Transform } from 'class-transformer';

export class UpdateGridConfigDto {
  @IsOptional()
  @IsNumber()
  @IsPositive()
  @Min(0.1)
  @Max(10)
  @Transform(({ value }) => value ? parseFloat(value) : undefined)
  gridSize?: number;

  @IsOptional()
  @IsNumber()
  @IsPositive()
  @Transform(({ value }) => value ? parseFloat(value) : undefined)
  basePrice?: number;

  @IsOptional()
  @IsNumber()
  @Min(0.1)
  @Max(5)
  @Transform(({ value }) => value ? parseFloat(value) : undefined)
  minGridSize?: number;

  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(10)
  @Transform(({ value }) => value ? parseFloat(value) : undefined)
  maxGridSize?: number;

  @IsOptional()
  @IsNumber()
  @Min(0.1)
  @Max(1)
  @Transform(({ value }) => value ? parseFloat(value) : undefined)
  maxPositionRatio?: number;

  @IsOptional()
  @IsNumber()
  @Min(0.01)
  @Max(0.5)
  @Transform(({ value }) => value ? parseFloat(value) : undefined)
  minPositionRatio?: number;

  @IsOptional()
  @IsBoolean()
  enableVolatilityAdjustment?: boolean;

  @IsOptional()
  @IsBoolean()
  enableRiskManagement?: boolean;

  @IsOptional()
  @IsBoolean()
  enableFundManagement?: boolean;

  @IsOptional()
  @IsNumber()
  @Min(7)
  @Max(365)
  @Transform(({ value }) => value ? parseInt(value) : undefined)
  volatilityWindow?: number;

  @IsOptional()
  @IsNumber()
  @Min(0.8)
  @Max(0.99)
  @Transform(({ value }) => value ? parseFloat(value) : undefined)
  ewmaLambda?: number;

  @IsOptional()
  @IsNumber()
  @Min(0.1)
  @Max(0.9)
  @Transform(({ value }) => value ? parseFloat(value) : undefined)
  hybridWeight?: number;

  @IsOptional()
  @IsNumber()
  @Min(300)
  @Max(86400)
  @Transform(({ value }) => value ? parseInt(value) : undefined)
  adjustmentInterval?: number;

  @IsOptional()
  @IsNumber()
  @IsPositive()
  @Transform(({ value }) => value ? parseFloat(value) : undefined)
  minTradeAmount?: number;

  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(10)
  @Transform(({ value }) => value ? parseInt(value) : undefined)
  maxRetries?: number;

  @IsOptional()
  @IsNumber()
  @Min(60)
  @Max(3600)
  @Transform(({ value }) => value ? parseInt(value) : undefined)
  riskCheckInterval?: number;

  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @IsOptional()
  @IsObject()
  additionalParams?: Record<string, any>;
}
