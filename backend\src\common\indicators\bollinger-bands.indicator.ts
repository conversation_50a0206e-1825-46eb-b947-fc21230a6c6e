import { BaseIndicator } from './base.indicator';

/**
 * 布林带指标
 */
export interface BollingerBandsResult {
  upper: number;
  middle: number;
  lower: number;
  bandwidth: number;
}

export class BollingerBandsIndicator extends BaseIndicator {
  private multiplier: number;
  
  constructor(period: number = 20, multiplier: number = 2) {
    super(period);
    this.multiplier = multiplier;
  }
  
  /**
   * 计算布林带值
   */
  calculate(): BollingerBandsResult[] {
    const closes = this.getClosePrices();
    if (closes.length < this.period) {
      return [];
    }
    
    const result: BollingerBandsResult[] = [];
    const smaValues = this.sma(closes, this.period);
    const stdValues = this.standardDeviation(closes, this.period);
    
    for (let i = 0; i < smaValues.length; i++) {
      const middle = smaValues[i];
      const std = stdValues[i];
      const upper = middle + (std * this.multiplier);
      const lower = middle - (std * this.multiplier);
      const bandwidth = (upper - lower) / middle;
      
      result.push({
        upper,
        middle,
        lower,
        bandwidth
      });
    }
    
    return result;
  }
  
  /**
   * 获取最新布林带值
   */
  getLatest(): BollingerBandsResult | null {
    const bands = this.calculate();
    return bands.length > 0 ? bands[bands.length - 1] : null;
  }
  
  /**
   * 判断价格是否触及上轨
   */
  isTouchingUpperBand(price: number, tolerance: number = 0.001): boolean {
    const latest = this.getLatest();
    if (!latest) return false;
    
    return Math.abs(price - latest.upper) / latest.upper <= tolerance || price >= latest.upper;
  }
  
  /**
   * 判断价格是否触及下轨
   */
  isTouchingLowerBand(price: number, tolerance: number = 0.001): boolean {
    const latest = this.getLatest();
    if (!latest) return false;
    
    return Math.abs(price - latest.lower) / latest.lower <= tolerance || price <= latest.lower;
  }
  
  /**
   * 判断是否处于震荡状态
   */
  isConsolidating(bandwidthThreshold: number = 0.04): boolean {
    const latest = this.getLatest();
    return latest !== null && latest.bandwidth < bandwidthThreshold;
  }
  
  /**
   * 计算中轨斜率 (用于判断趋势方向)
   */
  getMiddleBandSlope(lookback: number = 5): number | null {
    const bands = this.calculate();
    if (bands.length < lookback + 1) return null;
    
    const recent = bands.slice(-lookback - 1);
    const oldMiddle = recent[0].middle;
    const newMiddle = recent[recent.length - 1].middle;
    
    return (newMiddle - oldMiddle) / oldMiddle;
  }
}
