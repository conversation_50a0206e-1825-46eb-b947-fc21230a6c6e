import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  Index,
} from 'typeorm';

export enum RiskEventType {
  POSITION_LIMIT_EXCEEDED = 'position_limit_exceeded',
  CONSECUTIVE_FAILURES = 'consecutive_failures',
  VOLATILITY_SPIKE = 'volatility_spike',
  BALANCE_INSUFFICIENT = 'balance_insufficient',
  API_ERROR = 'api_error',
  NETWORK_ERROR = 'network_error',
  EMERGENCY_STOP = 'emergency_stop',
  MANUAL_INTERVENTION = 'manual_intervention',
}

export enum RiskLevel {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

@Entity('risk_events')
@Index(['symbol', 'createdAt'])
@Index(['eventType', 'createdAt'])
@Index(['riskLevel', 'createdAt'])
export class RiskEvent {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 20 })
  symbol: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  sessionId: string;

  @Column({
    type: 'enum',
    enum: RiskEventType,
  })
  eventType: RiskEventType;

  @Column({
    type: 'enum',
    enum: RiskLevel,
  })
  riskLevel: RiskLevel;

  @Column({ type: 'varchar', length: 200 })
  title: string;

  @Column({ type: 'text' })
  description: string;

  @Column({ type: 'decimal', precision: 18, scale: 8, nullable: true })
  triggerValue: number;

  @Column({ type: 'decimal', precision: 18, scale: 8, nullable: true })
  thresholdValue: number;

  @Column({ type: 'json', nullable: true })
  eventData: Record<string, any>;

  @Column({ type: 'boolean', default: false })
  isResolved: boolean;

  @Column({ type: 'timestamp', nullable: true })
  resolvedAt: Date;

  @Column({ type: 'text', nullable: true })
  resolution: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  resolvedBy: string;

  @Column({ type: 'json', nullable: true })
  actions: string[];

  @CreateDateColumn()
  createdAt: Date;
}
