import { MigrationInterface, QueryRunner, Table, Index } from 'typeorm';

export class CreateGridTradingTables1700000000000 implements MigrationInterface {
  name = 'CreateGridTradingTables1700000000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // 创建网格交易配置表
    await queryRunner.createTable(
      new Table({
        name: 'grid_trading_config',
        columns: [
          {
            name: 'id',
            type: 'varchar',
            isPrimary: true,
            generationStrategy: 'uuid',
            default: 'uuid_generate_v4()',
          },
          {
            name: 'symbol',
            type: 'varchar',
            length: '20',
            isNullable: false,
          },
          {
            name: 'basePrice',
            type: 'decimal',
            precision: 18,
            scale: 8,
            isNullable: true,
          },
          {
            name: 'gridSize',
            type: 'decimal',
            precision: 5,
            scale: 2,
            default: 2.0,
          },
          {
            name: 'minGridSize',
            type: 'decimal',
            precision: 5,
            scale: 2,
            default: 1.0,
          },
          {
            name: 'maxGridSize',
            type: 'decimal',
            precision: 5,
            scale: 2,
            default: 4.0,
          },
          {
            name: 'initialCapital',
            type: 'decimal',
            precision: 18,
            scale: 8,
            isNullable: false,
          },
          {
            name: 'minTradeAmount',
            type: 'decimal',
            precision: 18,
            scale: 8,
            default: 10,
          },
          {
            name: 'maxPositionRatio',
            type: 'decimal',
            precision: 5,
            scale: 4,
            default: 0.8,
          },
          {
            name: 'minPositionRatio',
            type: 'decimal',
            precision: 5,
            scale: 4,
            default: 0.1,
          },
          {
            name: 'enableVolatilityAdjustment',
            type: 'boolean',
            default: true,
          },
          {
            name: 'volatilityWindow',
            type: 'int',
            default: 52,
          },
          {
            name: 'ewmaLambda',
            type: 'decimal',
            precision: 5,
            scale: 4,
            default: 0.94,
          },
          {
            name: 'hybridWeight',
            type: 'decimal',
            precision: 5,
            scale: 4,
            default: 0.7,
          },
          {
            name: 'adjustmentInterval',
            type: 'int',
            default: 3600,
          },
          {
            name: 'enableRiskManagement',
            type: 'boolean',
            default: true,
          },
          {
            name: 'maxRetries',
            type: 'int',
            default: 5,
          },
          {
            name: 'isActive',
            type: 'boolean',
            default: false,
          },
          {
            name: 'createdAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updatedAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
            onUpdate: 'CURRENT_TIMESTAMP',
          },
        ],
        indices: [
          { name: 'IDX_GRID_CONFIG_SYMBOL', columnNames: ['symbol'] },
          { name: 'IDX_GRID_CONFIG_ACTIVE', columnNames: ['isActive'] },
        ],
      }),
      true,
    );

    // 创建交易仓位表
    await queryRunner.createTable(
      new Table({
        name: 'trading_position',
        columns: [
          {
            name: 'id',
            type: 'varchar',
            isPrimary: true,
            generationStrategy: 'uuid',
            default: 'uuid_generate_v4()',
          },
          {
            name: 'symbol',
            type: 'varchar',
            length: '20',
            isNullable: false,
          },
          {
            name: 'sessionId',
            type: 'varchar',
            length: '100',
            isNullable: false,
          },
          {
            name: 'side',
            type: 'enum',
            enum: ['LONG', 'SHORT'],
            isNullable: false,
          },
          {
            name: 'status',
            type: 'enum',
            enum: ['OPEN', 'CLOSED', 'CANCELLED'],
            default: "'OPEN'",
          },
          {
            name: 'entryPrice',
            type: 'decimal',
            precision: 18,
            scale: 8,
            isNullable: false,
          },
          {
            name: 'exitPrice',
            type: 'decimal',
            precision: 18,
            scale: 8,
            isNullable: true,
          },
          {
            name: 'quantity',
            type: 'decimal',
            precision: 18,
            scale: 8,
            isNullable: false,
          },
          {
            name: 'remainingQuantity',
            type: 'decimal',
            precision: 18,
            scale: 8,
            isNullable: false,
          },
          {
            name: 'totalValue',
            type: 'decimal',
            precision: 18,
            scale: 8,
            isNullable: false,
          },
          {
            name: 'realizedPnl',
            type: 'decimal',
            precision: 18,
            scale: 8,
            default: 0,
          },
          {
            name: 'fees',
            type: 'decimal',
            precision: 18,
            scale: 8,
            default: 0,
          },
          {
            name: 'gridLevel',
            type: 'int',
            default: 0,
          },
          {
            name: 'entryOrderId',
            type: 'varchar',
            length: '100',
            isNullable: true,
          },
          {
            name: 'exitOrderId',
            type: 'varchar',
            length: '100',
            isNullable: true,
          },
          {
            name: 'createdAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'closedAt',
            type: 'timestamp',
            isNullable: true,
          },
        ],
        indices: [
          { name: 'IDX_POSITION_SYMBOL_SESSION', columnNames: ['symbol', 'sessionId'] },
          { name: 'IDX_POSITION_STATUS', columnNames: ['status'] },
          { name: 'IDX_POSITION_CREATED', columnNames: ['createdAt'] },
        ],
      }),
      true,
    );

    // 创建订单历史表
    await queryRunner.createTable(
      new Table({
        name: 'order_history',
        columns: [
          {
            name: 'id',
            type: 'varchar',
            isPrimary: true,
            generationStrategy: 'uuid',
            default: 'uuid_generate_v4()',
          },
          {
            name: 'symbol',
            type: 'varchar',
            length: '20',
            isNullable: false,
          },
          {
            name: 'sessionId',
            type: 'varchar',
            length: '100',
            isNullable: false,
          },
          {
            name: 'exchangeOrderId',
            type: 'varchar',
            length: '100',
            isNullable: false,
          },
          {
            name: 'side',
            type: 'enum',
            enum: ['BUY', 'SELL'],
            isNullable: false,
          },
          {
            name: 'type',
            type: 'enum',
            enum: ['MARKET', 'LIMIT', 'STOP_LOSS', 'TAKE_PROFIT'],
            default: "'MARKET'",
          },
          {
            name: 'status',
            type: 'enum',
            enum: ['PENDING', 'OPEN', 'PARTIALLY_FILLED', 'FILLED', 'CANCELLED', 'REJECTED', 'EXPIRED'],
            default: "'PENDING'",
          },
          {
            name: 'price',
            type: 'decimal',
            precision: 18,
            scale: 8,
            isNullable: false,
          },
          {
            name: 'quantity',
            type: 'decimal',
            precision: 18,
            scale: 8,
            isNullable: false,
          },
          {
            name: 'filledQuantity',
            type: 'decimal',
            precision: 18,
            scale: 8,
            default: 0,
          },
          {
            name: 'remainingQuantity',
            type: 'decimal',
            precision: 18,
            scale: 8,
            isNullable: false,
          },
          {
            name: 'averagePrice',
            type: 'decimal',
            precision: 18,
            scale: 8,
            default: 0,
          },
          {
            name: 'totalValue',
            type: 'decimal',
            precision: 18,
            scale: 8,
            isNullable: false,
          },
          {
            name: 'fees',
            type: 'decimal',
            precision: 18,
            scale: 8,
            default: 0,
          },
          {
            name: 'profit',
            type: 'decimal',
            precision: 18,
            scale: 8,
            default: 0,
          },
          {
            name: 'gridLevel',
            type: 'int',
            isNullable: true,
          },
          {
            name: 'positionId',
            type: 'varchar',
            length: '100',
            isNullable: true,
          },
          {
            name: 'errorMessage',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'exchangeData',
            type: 'jsonb',
            isNullable: true,
          },
          {
            name: 'createdAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'filledAt',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'cancelledAt',
            type: 'timestamp',
            isNullable: true,
          },
        ],
        indices: [
          { name: 'IDX_ORDER_SYMBOL_SESSION', columnNames: ['symbol', 'sessionId'] },
          { name: 'IDX_ORDER_STATUS', columnNames: ['status'] },
          { name: 'IDX_ORDER_EXCHANGE_ID', columnNames: ['exchangeOrderId'] },
          { name: 'IDX_ORDER_CREATED', columnNames: ['createdAt'] },
        ],
      }),
      true,
    );

    // 创建风险事件表
    await queryRunner.createTable(
      new Table({
        name: 'risk_event',
        columns: [
          {
            name: 'id',
            type: 'varchar',
            isPrimary: true,
            generationStrategy: 'uuid',
            default: 'uuid_generate_v4()',
          },
          {
            name: 'symbol',
            type: 'varchar',
            length: '20',
            isNullable: false,
          },
          {
            name: 'sessionId',
            type: 'varchar',
            length: '100',
            isNullable: false,
          },
          {
            name: 'eventType',
            type: 'enum',
            enum: ['POSITION_LIMIT_EXCEEDED', 'CONSECUTIVE_FAILURES', 'VOLATILITY_SPIKE', 'BALANCE_INSUFFICIENT', 'API_ERROR', 'NETWORK_ERROR', 'EMERGENCY_STOP', 'MANUAL_INTERVENTION'],
            isNullable: false,
          },
          {
            name: 'riskLevel',
            type: 'enum',
            enum: ['LOW', 'MEDIUM', 'HIGH', 'CRITICAL'],
            default: "'MEDIUM'",
          },
          {
            name: 'title',
            type: 'varchar',
            length: '200',
            isNullable: false,
          },
          {
            name: 'description',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'triggerValue',
            type: 'decimal',
            precision: 18,
            scale: 8,
            isNullable: true,
          },
          {
            name: 'thresholdValue',
            type: 'decimal',
            precision: 18,
            scale: 8,
            isNullable: true,
          },
          {
            name: 'eventData',
            type: 'jsonb',
            isNullable: true,
          },
          {
            name: 'actions',
            type: 'text',
            isArray: true,
            isNullable: true,
          },
          {
            name: 'isResolved',
            type: 'boolean',
            default: false,
          },
          {
            name: 'resolvedAt',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'resolvedBy',
            type: 'varchar',
            length: '100',
            isNullable: true,
          },
          {
            name: 'createdAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
        ],
        indices: [
          { name: 'IDX_RISK_EVENT_SYMBOL_SESSION', columnNames: ['symbol', 'sessionId'] },
          { name: 'IDX_RISK_EVENT_TYPE', columnNames: ['eventType'] },
          { name: 'IDX_RISK_EVENT_LEVEL', columnNames: ['riskLevel'] },
          { name: 'IDX_RISK_EVENT_RESOLVED', columnNames: ['isResolved'] },
          { name: 'IDX_RISK_EVENT_CREATED', columnNames: ['createdAt'] },
        ],
      }),
      true,
    );

    // 创建波动率数据表
    await queryRunner.createTable(
      new Table({
        name: 'volatility_data',
        columns: [
          {
            name: 'id',
            type: 'varchar',
            isPrimary: true,
            generationStrategy: 'uuid',
            default: 'uuid_generate_v4()',
          },
          {
            name: 'symbol',
            type: 'varchar',
            length: '20',
            isNullable: false,
          },
          {
            name: 'timestamp',
            type: 'timestamp',
            isNullable: false,
          },
          {
            name: 'price',
            type: 'decimal',
            precision: 18,
            scale: 8,
            isNullable: false,
          },
          {
            name: 'volume',
            type: 'decimal',
            precision: 18,
            scale: 8,
            default: 0,
          },
          {
            name: 'logReturn',
            type: 'decimal',
            precision: 18,
            scale: 8,
            default: 0,
          },
          {
            name: 'traditionalVolatility',
            type: 'decimal',
            precision: 18,
            scale: 8,
            isNullable: false,
          },
          {
            name: 'ewmaVolatility',
            type: 'decimal',
            precision: 18,
            scale: 8,
            isNullable: false,
          },
          {
            name: 'hybridVolatility',
            type: 'decimal',
            precision: 18,
            scale: 8,
            isNullable: false,
          },
          {
            name: 'annualizedVolatility',
            type: 'decimal',
            precision: 18,
            scale: 8,
            isNullable: false,
          },
          {
            name: 'volumeWeightedVolatility',
            type: 'decimal',
            precision: 18,
            scale: 8,
            isNullable: true,
          },
          {
            name: 'smoothedVolatility',
            type: 'decimal',
            precision: 18,
            scale: 8,
            isNullable: false,
          },
          {
            name: 'windowSize',
            type: 'int',
            default: 52,
          },
          {
            name: 'metadata',
            type: 'jsonb',
            isNullable: true,
          },
          {
            name: 'createdAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
        ],
        indices: [
          { name: 'IDX_VOLATILITY_SYMBOL_TIME', columnNames: ['symbol', 'timestamp'] },
          { name: 'IDX_VOLATILITY_TIMESTAMP', columnNames: ['timestamp'] },
        ],
      }),
      true,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable('volatility_data');
    await queryRunner.dropTable('risk_event');
    await queryRunner.dropTable('order_history');
    await queryRunner.dropTable('trading_position');
    await queryRunner.dropTable('grid_trading_config');
  }
}
