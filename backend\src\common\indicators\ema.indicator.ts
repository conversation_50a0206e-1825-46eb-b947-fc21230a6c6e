import { BaseIndicator } from './base.indicator';

/**
 * 指数移动平均线 (EMA)
 */
export class EMAIndicator extends BaseIndicator {
  private multiplier: number;
  
  constructor(period: number) {
    super(period);
    this.multiplier = 2 / (period + 1);
  }
  
  /**
   * 计算EMA值
   */
  calculate(): number[] {
    const closes = this.getClosePrices();
    if (closes.length < this.period) {
      return [];
    }
    
    const result: number[] = [];
    
    // 第一个EMA值使用SMA
    const firstSMA = closes.slice(0, this.period).reduce((a, b) => a + b, 0) / this.period;
    result.push(firstSMA);
    
    // 后续EMA值使用递归公式: EMA = (Close - EMA_prev) * multiplier + EMA_prev
    for (let i = this.period; i < closes.length; i++) {
      const ema = (closes[i] - result[result.length - 1]) * this.multiplier + result[result.length - 1];
      result.push(ema);
    }
    
    return result;
  }
  
  /**
   * 获取最新EMA值
   */
  getLatest(): number | null {
    const emaValues = this.calculate();
    return emaValues.length > 0 ? emaValues[emaValues.length - 1] : null;
  }
  
  /**
   * 获取指定索引的EMA值
   */
  getValue(index: number): number | null {
    const emaValues = this.calculate();
    if (index < 0 || index >= emaValues.length) {
      return null;
    }
    return emaValues[index];
  }
}
