import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  Index,
} from 'typeorm';

@Entity('volatility_data')
@Index(['symbol', 'timestamp'])
@Index(['symbol', 'timeframe', 'timestamp'])
export class VolatilityData {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 20 })
  symbol: string;

  @Column({ type: 'varchar', length: 10, default: '4h' })
  timeframe: string;

  @Column({ type: 'timestamp' })
  timestamp: Date;

  @Column({ type: 'decimal', precision: 18, scale: 8 })
  price: number;

  @Column({ type: 'decimal', precision: 18, scale: 8 })
  volume: number;

  @Column({ type: 'decimal', precision: 10, scale: 8, nullable: true })
  logReturn: number;

  @Column({ type: 'decimal', precision: 10, scale: 8, nullable: true })
  traditionalVolatility: number;

  @Column({ type: 'decimal', precision: 10, scale: 8, nullable: true })
  ewmaVolatility: number;

  @Column({ type: 'decimal', precision: 10, scale: 8, nullable: true })
  hybridVolatility: number;

  @Column({ type: 'decimal', precision: 10, scale: 8, nullable: true })
  annualizedVolatility: number;

  @Column({ type: 'decimal', precision: 10, scale: 8, nullable: true })
  volumeWeightedVolatility: number;

  @Column({ type: 'decimal', precision: 10, scale: 8, nullable: true })
  smoothedVolatility: number;

  @Column({ type: 'int', nullable: true })
  windowSize: number;

  @Column({ type: 'json', nullable: true })
  metadata: Record<string, any>;

  @CreateDateColumn()
  createdAt: Date;
}
