import { <PERSON>du<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SignalController } from './signal.controller';
import { SignalService } from './signal.service';
import { SignalEntity } from '../../entities/signal.entity';
import { MarketDataModule } from '../market-data/market-data.module';
import { WebsocketModule } from '../websocket/websocket.module';
import { NotificationService } from '../../common/services/notification.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([SignalEntity]),
    MarketDataModule,
    WebsocketModule,
  ],
  controllers: [SignalController],
  providers: [SignalService, NotificationService],
  exports: [SignalService],
})
export class SignalModule {}
