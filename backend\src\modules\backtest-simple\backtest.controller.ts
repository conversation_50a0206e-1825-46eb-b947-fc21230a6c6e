import { Controller, Post, Body, Get, Param } from '@nestjs/common';

@Controller('backtest')
export class BacktestController {
  @Post()
  async startBacktest(@Body() backtestConfig: any) {
    // 返回模拟回测结果
    return {
      jobId: 'demo-job-' + Date.now(),
      status: 'COMPLETED',
      results: {
        totalTrades: 15,
        winningTrades: 9,
        losingTrades: 6,
        winRate: 60.0,
        totalReturn: 12.5,
        maxDrawdown: -8.2,
        sharpeRatio: 1.45,
        profitFactor: 1.8,
        initialCapital: backtestConfig.initialCapital || 10000,
        finalCapital: (backtestConfig.initialCapital || 10000) * 1.125,
        trades: [
          {
            entryTime: '2025-06-15T10:30:00Z',
            exitTime: '2025-06-16T14:20:00Z',
            pair: backtestConfig.pair || 'BTC/USDT',
            side: 'buy',
            entryPrice: 67500,
            exitPrice: 69200,
            quantity: 0.1,
            pnl: 170,
            pnlPercent: 2.52,
          },
          {
            entryTime: '2025-06-20T09:15:00Z',
            exitTime: '2025-06-21T16:45:00Z',
            pair: backtestConfig.pair || 'BTC/USDT',
            side: 'buy',
            entryPrice: 68800,
            exitPrice: 67100,
            quantity: 0.1,
            pnl: -170,
            pnlPercent: -2.47,
          },
        ],
      },
    };
  }

  @Get(':jobId/status')
  async getBacktestStatus(@Param('jobId') jobId: string) {
    // 返回模拟状态
    return {
      jobId,
      status: 'COMPLETED',
      progress: 100,
      message: '回测完成',
    };
  }

  @Get(':jobId/results')
  async getBacktestResults(@Param('jobId') jobId: string) {
    // 返回符合 BacktestResult 接口的模拟回测结果
    const now = Date.now();
    return {
      id: jobId,
      request: {
        asset: 'BTC/USDT',
        strategy: 'TREND_FOLLOWING',
        startDate: '2025-06-12',
        endDate: '2025-09-12',
        initialCapital: 10000,
      },
      trades: [
        {
          id: 'trade-1',
          entryTime: new Date('2025-06-15T10:30:00Z').getTime(),
          exitTime: new Date('2025-06-16T14:20:00Z').getTime(),
          asset: 'BTC/USDT',
          side: 'buy',
          entryPrice: 67500,
          exitPrice: 69200,
          quantity: 0.1,
          pnl: 170,
          reason: '趋势跟踪信号',
        },
        {
          id: 'trade-2',
          entryTime: new Date('2025-06-20T09:15:00Z').getTime(),
          exitTime: new Date('2025-06-21T16:45:00Z').getTime(),
          asset: 'BTC/USDT',
          side: 'buy',
          entryPrice: 68800,
          exitPrice: 67100,
          quantity: 0.1,
          pnl: -170,
          reason: '止损信号',
        },
      ],
      performance: {
        totalReturn: 1250, // 总收益金额
        totalReturnPercent: 12.5, // 收益率百分比
        maxDrawdown: -8.2,
        winRate: 60.0,
        profitFactor: 1.8,
        sharpeRatio: 1.45,
        totalTrades: 15,
        winningTrades: 9,
        losingTrades: 6,
      },
      equityCurve: [
        { timestamp: new Date('2025-06-12').getTime(), equity: 10000 },
        { timestamp: new Date('2025-06-15').getTime(), equity: 10170 },
        { timestamp: new Date('2025-06-20').getTime(), equity: 10000 },
        { timestamp: new Date('2025-09-12').getTime(), equity: 11250 },
      ],
      createdAt: now - 300000, // 5分钟前创建
      completedAt: now,
    };
  }
}
