import { Controller, Post, Get, Body } from '@nestjs/common';
import { AnalysisMemoryService } from './analysis-memory.service';

@Controller('analysis')
export class AnalysisMemoryController {
  constructor(private readonly analysisService: AnalysisMemoryService) {}

  @Post('analyze')
  async analyze(@Body() body: { asset: string; strategy: string }) {
    return await this.analysisService.analyzeSymbol(body.asset, body.strategy);
  }

  @Get('history')
  async getHistory() {
    return await this.analysisService.getAnalysisHistory();
  }
}
