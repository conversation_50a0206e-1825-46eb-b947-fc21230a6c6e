import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigurationController } from './configuration.controller';
import { ConfigurationService } from './configuration.service';
import { SystemConfigEntity } from '../../entities/system-config.entity';
import { MarketDataModule } from '../market-data/market-data.module';
import { NotificationService } from '../../common/services/notification.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([SystemConfigEntity]),
    MarketDataModule,
  ],
  controllers: [ConfigurationController],
  providers: [ConfigurationService, NotificationService],
  exports: [ConfigurationService],
})
export class ConfigurationModule {}
