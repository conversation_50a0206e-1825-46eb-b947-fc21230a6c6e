import { io, Socket } from 'socket.io-client';
import { TradingSignal, BacktestJob, WS_EVENTS } from '@signal-dash/shared';

const WS_URL = import.meta.env.VITE_WS_URL || 'http://localhost:3003';

class WebSocketService {
  private _socket: Socket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;

  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this._socket?.connected) {
        resolve();
        return;
      }

      this._socket = io(WS_URL, {
        transports: ['websocket'],
        timeout: 5000,
      });

      this._socket.on('connect', () => {
        console.log('WebSocket连接成功');
        this.reconnectAttempts = 0;
        resolve();
      });

      this._socket.on('disconnect', (reason) => {
        console.log('WebSocket断开连接:', reason);
        this.handleReconnect();
      });

      this._socket.on('connect_error', (error) => {
        console.error('WebSocket连接错误:', error);
        reject(error);
      });
    });
  }

  disconnect(): void {
    if (this._socket) {
      this._socket.disconnect();
      this._socket = null;
    }
  }

  private handleReconnect(): void {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      console.log(`尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);
      
      setTimeout(() => {
        this.connect().catch(console.error);
      }, this.reconnectDelay * this.reconnectAttempts);
    }
  }

  // 订阅信号更新
  subscribeToSignals(callback: (signal: TradingSignal) => void): void {
    if (!this._socket) return;

    this._socket.emit('subscribe:signals');
    this._socket.on(WS_EVENTS.SIGNAL_CREATED, callback);
  }

  unsubscribeFromSignals(): void {
    if (!this._socket) return;

    this._socket.emit('unsubscribe:signals');
    this._socket.off(WS_EVENTS.SIGNAL_CREATED);
  }

  // 订阅回测进度
  subscribeToBacktest(
    jobId: string,
    onProgress: (data: { jobId: string; progress: number }) => void,
    onCompleted: (job: BacktestJob) => void
  ): void {
    if (!this._socket) return;

    this._socket.emit('subscribe:backtest', { jobId });
    this._socket.on(WS_EVENTS.BACKTEST_PROGRESS, onProgress);
    this._socket.on(WS_EVENTS.BACKTEST_COMPLETED, onCompleted);
  }

  unsubscribeFromBacktest(jobId: string): void {
    if (!this._socket) return;

    this._socket.emit('unsubscribe:backtest', { jobId });
    this._socket.off(WS_EVENTS.BACKTEST_PROGRESS);
    this._socket.off(WS_EVENTS.BACKTEST_COMPLETED);
  }

  // 订阅市场数据
  subscribeToMarketData(
    assets: string[],
    callback: (data: { asset: string; data: any }) => void
  ): void {
    if (!this._socket) return;

    this._socket.emit('subscribe:market', { assets });
    this._socket.on(WS_EVENTS.MARKET_DATA_UPDATE, callback);
  }

  unsubscribeFromMarketData(): void {
    if (!this._socket) return;

    this._socket.off(WS_EVENTS.MARKET_DATA_UPDATE);
  }

  isConnected(): boolean {
    return this._socket?.connected || false;
  }

  get socket(): Socket | null {
    return this._socket;
  }
}

export const websocketService = new WebSocketService();
