import { BullModuleAsyncOptions } from '@nestjs/bull';
import { ConfigService } from '@nestjs/config';

export const redisConfig: BullModuleAsyncOptions = {
  useFactory: (configService: ConfigService) => ({
    redis: {
      host: configService.get('REDIS_HOST', 'localhost'),
      port: configService.get('REDIS_PORT', 6379),
      password: configService.get('REDIS_PASSWORD'),
    },
  }),
  inject: [ConfigService],
};
