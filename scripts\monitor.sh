#!/bin/bash

# Signal-Dash Pro 系统监控脚本

set -e

echo "📊 Signal-Dash Pro 系统监控"
echo "================================"

# 检查Docker服务状态
echo "🐳 Docker服务状态:"
docker-compose ps

echo ""
echo "💾 系统资源使用:"
docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}\t{{.BlockIO}}"

echo ""
echo "📈 服务健康检查:"

# 检查后端API
if curl -f http://localhost:3000/api/health > /dev/null 2>&1; then
    echo "✅ 后端API: 正常"
else
    echo "❌ 后端API: 异常"
fi

# 检查前端
if curl -f http://localhost:8080 > /dev/null 2>&1; then
    echo "✅ 前端服务: 正常"
else
    echo "❌ 前端服务: 异常"
fi

# 检查Redis
if docker-compose exec -T redis redis-cli ping > /dev/null 2>&1; then
    echo "✅ Redis: 正常"
else
    echo "❌ Redis: 异常"
fi

echo ""
echo "📋 最近日志 (最后20行):"
echo "--- 后端日志 ---"
docker-compose logs --tail=10 backend

echo ""
echo "--- Redis日志 ---"
docker-compose logs --tail=5 redis

echo ""
echo "💽 数据库状态:"
if [ -f "data/signal-dash.db" ]; then
    DB_SIZE=$(du -h data/signal-dash.db | cut -f1)
    echo "✅ 数据库文件: $DB_SIZE"
else
    echo "⚠️  数据库文件不存在"
fi

echo ""
echo "📁 磁盘使用:"
df -h | grep -E "(Filesystem|/dev/)"

echo ""
echo "🔄 实时监控命令:"
echo "   docker-compose logs -f          # 查看实时日志"
echo "   docker stats                    # 查看资源使用"
echo "   docker-compose exec backend sh  # 进入后端容器"
echo "   docker-compose exec redis sh    # 进入Redis容器"
