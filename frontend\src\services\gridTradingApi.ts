import axios from 'axios';
import {
  GridTradingConfig,
  StartGridTradingDto,

  GridTradingSession,
  GridStatus,
  TradingPosition,
  OrderHistory,
  RiskEvent,
  GridPerformance,
  PositionSummary,
  RiskMetrics,
  MarketData,
  BalanceInfo,
} from '../types/gridTrading';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3003/api';

const gridApiClient = axios.create({
  baseURL: `${API_BASE_URL}/grid-trading`,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
gridApiClient.interceptors.request.use(
  (config) => {
    console.log(`网格交易API请求: ${config.method?.toUpperCase()} ${config.url}`);
    return config;
  },
  (error) => Promise.reject(error)
);

// 响应拦截器
gridApiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('网格交易API请求失败:', error.response?.data || error.message);
    return Promise.reject(error);
  }
);

export const gridTradingApi = {
  // 配置管理
  getConfigs: (): Promise<GridTradingConfig[]> =>
    gridApiClient.get('/configs').then(res => res.data),

  getConfig: (symbol: string): Promise<GridTradingConfig> =>
    gridApiClient.get(`/configs/${symbol}`).then(res => res.data),

  createConfig: (config: Omit<GridTradingConfig, 'id' | 'createdAt' | 'updatedAt'>): Promise<GridTradingConfig> =>
    gridApiClient.post('/configs', config).then(res => res.data),

  updateConfig: (id: string, config: Partial<GridTradingConfig>): Promise<GridTradingConfig> =>
    gridApiClient.put(`/configs/${id}`, config).then(res => res.data),

  deleteConfig: (id: string): Promise<{ success: boolean }> =>
    gridApiClient.delete(`/configs/${id}`).then(res => res.data),

  // 交易控制
  startGrid: (request: StartGridTradingDto): Promise<{ sessionId: string; message: string }> =>
    gridApiClient.post('/start', request).then(res => res.data),

  stopGrid: (symbol: string): Promise<{ success: boolean; message: string }> =>
    gridApiClient.post(`/stop/${symbol}`).then(res => res.data),

  emergencyStop: (symbol: string): Promise<{ success: boolean; message: string }> =>
    gridApiClient.post(`/emergency-stop/${symbol}`).then(res => res.data),

  // 状态查询
  getStatus: (symbol: string): Promise<{
    isRunning: boolean;
    sessionId?: string;
    startTime?: string;
    gridStatus?: GridStatus;
    riskMetrics?: RiskMetrics;
    positionSummary?: PositionSummary;
  }> =>
    gridApiClient.get(`/status/${symbol}`).then(res => res.data),

  getActiveSessions: (): Promise<GridTradingSession[]> =>
    gridApiClient.get('/sessions').then(res => res.data),

  getSessionDetails: (sessionId: string): Promise<{
    session: GridTradingSession;
    gridStatus: GridStatus;
    riskMetrics: RiskMetrics;
    positionSummary: PositionSummary;
  }> =>
    gridApiClient.get(`/sessions/${sessionId}`).then(res => res.data),

  // 仓位管理
  getPositions: (symbol: string, params?: {
    sessionId?: string;
    status?: string;
    limit?: number;
    offset?: number;
  }): Promise<{
    positions: TradingPosition[];
    total: number;
    summary: PositionSummary;
  }> =>
    gridApiClient.get(`/positions/${symbol}`, { params }).then(res => res.data),

  getPositionById: (positionId: string): Promise<TradingPosition> =>
    gridApiClient.get(`/positions/detail/${positionId}`).then(res => res.data),

  closePosition: (positionId: string): Promise<{ success: boolean; message: string }> =>
    gridApiClient.post(`/positions/${positionId}/close`).then(res => res.data),

  // 订单管理
  getOrders: (symbol: string, params?: {
    sessionId?: string;
    status?: string;
    limit?: number;
    offset?: number;
  }): Promise<{
    orders: OrderHistory[];
    total: number;
  }> =>
    gridApiClient.get(`/orders/${symbol}`, { params }).then(res => res.data),

  getOrderById: (orderId: string): Promise<OrderHistory> =>
    gridApiClient.get(`/orders/detail/${orderId}`).then(res => res.data),

  cancelOrder: (orderId: string): Promise<{ success: boolean; message: string }> =>
    gridApiClient.post(`/orders/${orderId}/cancel`).then(res => res.data),

  // 表现分析
  getPerformance: (symbol: string, params?: {
    sessionId?: string;
    startDate?: string;
    endDate?: string;
  }): Promise<GridPerformance> =>
    gridApiClient.get(`/performance/${symbol}`, { params }).then(res => res.data),

  getPerformanceComparison: (symbols: string[], params?: {
    startDate?: string;
    endDate?: string;
  }): Promise<{
    [symbol: string]: GridPerformance;
  }> =>
    gridApiClient.post('/performance/compare', { symbols, ...params }).then(res => res.data),

  // 风险管理
  getRiskEvents: (symbol?: string, params?: {
    riskLevel?: string;
    eventType?: string;
    isResolved?: boolean;
    limit?: number;
    offset?: number;
  }): Promise<{
    events: RiskEvent[];
    total: number;
  }> =>
    gridApiClient.get('/risk-events', { 
      params: { symbol, ...params } 
    }).then(res => res.data),

  resolveRiskEvent: (eventId: string, resolvedBy: string): Promise<{ success: boolean }> =>
    gridApiClient.post(`/risk-events/${eventId}/resolve`, { resolvedBy }).then(res => res.data),

  getRiskMetrics: (symbol: string): Promise<RiskMetrics> =>
    gridApiClient.get(`/risk-metrics/${symbol}`).then(res => res.data),

  // 市场数据
  getMarketData: (symbol: string): Promise<MarketData> =>
    gridApiClient.get(`/market-data/${symbol}`).then(res => res.data),

  getBalance: (): Promise<BalanceInfo[]> =>
    gridApiClient.get('/balance').then(res => res.data),

  // 系统监控
  getSystemHealth: (): Promise<{
    database: boolean;
    exchange: boolean;
    redis: boolean;
    taskQueue: boolean;
    activeSessions: number;
    totalPositions: number;
    dailyVolume: number;
  }> =>
    gridApiClient.get('/health').then(res => res.data),

  getSystemStats: (): Promise<{
    totalConfigs: number;
    activeConfigs: number;
    totalSessions: number;
    activeSessions: number;
    totalTrades: number;
    todayTrades: number;
    totalProfit: number;
    todayProfit: number;
    riskEvents: {
      total: number;
      unresolved: number;
      critical: number;
    };
  }> =>
    gridApiClient.get('/stats').then(res => res.data),

  // 配置测试
  testConfig: (config: Partial<GridTradingConfig>): Promise<{
    isValid: boolean;
    warnings: string[];
    suggestions: string[];
    estimatedGridLevels: number;
    estimatedCapitalUsage: number;
  }> =>
    gridApiClient.post('/test-config', config).then(res => res.data),

  // 模拟交易
  simulateGrid: (request: StartGridTradingDto & {
    duration: number; // 模拟天数
    historicalData?: boolean;
  }): Promise<{
    jobId: string;
    message: string;
  }> =>
    gridApiClient.post('/simulate', request).then(res => res.data),

  getSimulationResult: (jobId: string): Promise<{
    status: 'pending' | 'running' | 'completed' | 'failed';
    progress?: number;
    result?: GridPerformance;
    error?: string;
  }> =>
    gridApiClient.get(`/simulate/${jobId}`).then(res => res.data),
};
