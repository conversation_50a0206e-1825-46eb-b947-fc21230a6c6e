#!/bin/bash

# Signal-Dash Pro 启动脚本
# 用于开发环境快速启动

set -e

echo "🚀 启动 Signal-Dash Pro..."

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker未运行，请先启动Docker"
    exit 1
fi

# 检查Docker Compose是否可用
if ! command -v docker-compose > /dev/null 2>&1; then
    echo "❌ Docker Compose未安装"
    exit 1
fi

# 创建必要的目录
echo "📁 创建数据目录..."
mkdir -p data
mkdir -p logs

# 检查环境文件
if [ ! -f .env ]; then
    echo "📝 创建环境配置文件..."
    cp .env.example .env
    echo "⚠️  请编辑 .env 文件配置您的参数"
fi

# 启动服务
echo "🐳 启动Docker服务..."
docker-compose up -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 10

# 检查服务状态
echo "🔍 检查服务状态..."
docker-compose ps

# 显示访问信息
echo ""
echo "✅ Signal-Dash Pro 启动完成！"
echo ""
echo "📊 访问地址:"
echo "   前端界面: http://localhost:8080"
echo "   后端API:  http://localhost:3000/api"
echo "   WebSocket: ws://localhost:3000"
echo ""
echo "📋 管理命令:"
echo "   查看日志: docker-compose logs -f"
echo "   停止服务: docker-compose down"
echo "   重启服务: docker-compose restart"
echo ""
echo "🔧 配置文件:"
echo "   系统配置: .env"
echo "   数据目录: ./data"
echo "   日志目录: ./logs"
echo ""

# 检查服务健康状态
echo "🏥 检查服务健康状态..."
sleep 5

# 检查后端健康状态
if curl -f http://localhost:3000/api/health > /dev/null 2>&1; then
    echo "✅ 后端服务正常"
else
    echo "⚠️  后端服务可能未完全启动，请稍等片刻"
fi

# 检查Redis连接
if docker-compose exec -T redis redis-cli ping > /dev/null 2>&1; then
    echo "✅ Redis服务正常"
else
    echo "⚠️  Redis服务异常"
fi

echo ""
echo "🎉 启动完成！请访问 http://localhost:8080 开始使用"
