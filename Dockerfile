# 多阶段构建 Dockerfile
FROM node:18-alpine AS base
# 安装 pnpm
RUN npm install -g pnpm

# 安装依赖阶段
FROM base AS deps
WORKDIR /app
COPY package*.json ./
COPY backend/package*.json ./backend/
COPY frontend/package*.json ./frontend/
COPY shared/package*.json ./shared/
COPY pnpm-workspace.yaml ./
COPY pnpm-lock.yaml ./
RUN pnpm install --frozen-lockfile

# 构建阶段
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .
RUN pnpm run build

# 生产运行阶段
FROM base AS runner
WORKDIR /app

# 创建非root用户
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

# 复制构建产物
COPY --from=builder /app/backend/dist ./backend/dist
COPY --from=builder /app/frontend/dist ./frontend/dist
COPY --from=builder /app/shared/dist ./shared/dist
COPY --from=builder /app/backend/package*.json ./backend/
COPY --from=builder /app/shared/package*.json ./shared/

# 安装生产依赖
RUN cd backend && pnpm install --frozen-lockfile --prod && pnpm store prune
RUN cd shared && pnpm install --frozen-lockfile --prod && pnpm store prune

# 设置权限
RUN chown -R nextjs:nodejs /app
USER nextjs

# 暴露端口
EXPOSE 3000 3001

# 启动命令
CMD ["node", "backend/dist/main.js"]
