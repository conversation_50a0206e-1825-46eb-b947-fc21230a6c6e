import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BullModule } from '@nestjs/bull';
import { GridTradingController } from './grid-trading.controller';
import { GridTradingService } from './grid-trading.service';
import { GridStrategyService } from './strategies/grid-strategy.service';
import { VolatilityCalculatorService } from './strategies/volatility-calculator.service';
import { RiskManagerService } from './risk-management/risk-manager.service';
import { PositionControllerService } from './risk-management/position-controller.service';
import { ExchangeClientService } from './exchange/exchange-client.service';
import { OrderManagerService } from './exchange/order-manager.service';
import { GridTradingConfig } from './entities/grid-trading-config.entity';
import { TradingPosition } from './entities/trading-position.entity';
import { OrderHistory } from './entities/order-history.entity';
import { RiskEvent } from './entities/risk-event.entity';
import { VolatilityData } from './entities/volatility-data.entity';
import { GridTradingProcessor } from './processors/grid-trading.processor';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      GridTradingConfig,
      TradingPosition,
      OrderHistory,
      RiskEvent,
      VolatilityData,
    ]),
    BullModule.registerQueue({
      name: 'grid-trading',
    }),
  ],
  controllers: [GridTradingController],
  providers: [
    GridTradingService,
    GridStrategyService,
    VolatilityCalculatorService,
    RiskManagerService,
    PositionControllerService,
    ExchangeClientService,
    OrderManagerService,
    GridTradingProcessor,
  ],
  exports: [
    GridTradingService,
    ExchangeClientService,
  ],
})
export class GridTradingModule {}
