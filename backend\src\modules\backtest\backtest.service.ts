import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { BacktestRequestDto } from './dto/backtest-request.dto';
import { BacktestJobEntity } from '../../entities/backtest-job.entity';
import { BacktestJob, BacktestResult, BacktestRequest } from '@signal-dash/shared';
import { generateId } from '@signal-dash/shared';

@Injectable()
export class BacktestService {
  private readonly logger = new Logger(BacktestService.name);

  constructor(
    @InjectRepository(BacktestJobEntity)
    private backtestJobRepository: Repository<BacktestJobEntity>,
    @InjectQueue('backtest')
    private backtestQueue: Queue
  ) {}

  /**
   * 启动回测任务
   */
  async startBacktest(requestDto: BacktestRequestDto): Promise<string> {
    const jobId = generateId();
    const now = Date.now();

    // 验证日期范围
    const startTime = new Date(requestDto.startDate).getTime();
    const endTime = new Date(requestDto.endDate).getTime();
    
    if (startTime >= endTime) {
      throw new Error('开始日期必须早于结束日期');
    }
    
    if (endTime > Date.now()) {
      throw new Error('结束日期不能是未来时间');
    }

    const request: BacktestRequest = {
      asset: requestDto.asset,
      strategy: requestDto.strategy,
      startDate: requestDto.startDate,
      endDate: requestDto.endDate,
      initialCapital: requestDto.initialCapital,
    };

    // 创建数据库记录
    const jobEntity = this.backtestJobRepository.create({
      id: jobId,
      status: 'PENDING',
      progress: 0,
      request,
      createdAt: now,
      updatedAt: now,
    });

    await this.backtestJobRepository.save(jobEntity);

    // 添加到任务队列
    await this.backtestQueue.add('run-backtest', {
      jobId,
      request,
    }, {
      jobId,
      removeOnComplete: 10,
      removeOnFail: 5,
    });

    this.logger.log(`回测任务已创建: ${jobId}`);
    return jobId;
  }

  /**
   * 获取回测状态
   */
  async getBacktestStatus(jobId: string): Promise<BacktestJob | null> {
    const jobEntity = await this.backtestJobRepository.findOne({
      where: { id: jobId }
    });

    if (!jobEntity) {
      return null;
    }

    return {
      id: jobEntity.id,
      status: jobEntity.status,
      progress: jobEntity.progress,
      result: jobEntity.result,
      error: jobEntity.error,
      createdAt: jobEntity.createdAt,
      updatedAt: jobEntity.updatedAt,
    };
  }

  /**
   * 获取回测结果
   */
  async getBacktestResults(jobId: string): Promise<BacktestResult | null> {
    const jobEntity = await this.backtestJobRepository.findOne({
      where: { id: jobId }
    });

    if (!jobEntity || jobEntity.status !== 'COMPLETED' || !jobEntity.result) {
      return null;
    }

    return jobEntity.result;
  }

  /**
   * 更新回测进度
   */
  async updateBacktestProgress(jobId: string, progress: number): Promise<void> {
    await this.backtestJobRepository.update(jobId, {
      progress,
      updatedAt: Date.now(),
    });
  }

  /**
   * 更新回测状态
   */
  async updateBacktestStatus(
    jobId: string,
    status: 'RUNNING' | 'COMPLETED' | 'FAILED',
    result?: BacktestResult,
    error?: string
  ): Promise<void> {
    const updateData: any = {
      status,
      updatedAt: Date.now(),
    };

    if (result) {
      updateData.result = result;
    }

    if (error) {
      updateData.error = error;
    }

    if (status === 'COMPLETED') {
      updateData.progress = 100;
    }

    await this.backtestJobRepository.update(jobId, updateData);
  }

  /**
   * 获取所有回测任务
   */
  async getAllBacktestJobs(limit: number = 50): Promise<BacktestJob[]> {
    const jobEntities = await this.backtestJobRepository.find({
      order: { dbCreatedAt: 'DESC' },
      take: limit,
    });

    return jobEntities.map(entity => ({
      id: entity.id,
      status: entity.status,
      progress: entity.progress,
      result: entity.result,
      error: entity.error,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
    }));
  }
}
