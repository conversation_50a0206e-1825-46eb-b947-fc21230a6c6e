import { TradingPair, TimeFrame } from './types.js';

// 支持的交易对
export const SUPPORTED_PAIRS: TradingPair[] = [
  'BTC/USDT',
  'ETH/USDT',
  'SOL/USDT'
];

// 币安交易对映射
export const BINANCE_SYMBOL_MAP: Record<TradingPair, string> = {
  'BTC/USDT': 'BTCUSDT',
  'ETH/USDT': 'ETHUSDT',
  'SOL/USDT': 'SOLUSDT'
};

// 时间框架映射
export const TIMEFRAME_MAP: Record<TimeFrame, string> = {
  [TimeFrame.M15]: '15m',
  [TimeFrame.H1]: '1h',
  [TimeFrame.H4]: '4h',
  [TimeFrame.D1]: '1d'
};

// 技术指标参数
export const INDICATOR_PARAMS = {
  EMA: {
    SHORT: 20,
    MEDIUM: 50,
    LONG: 200
  },
  RSI: {
    PERIOD: 14,
    OVERBOUGHT: 70,
    OVERSOLD: 30
  },
  ATR: {
    PERIOD: 14
  },
  BOLLINGER_BANDS: {
    PERIOD: 20,
    MULTIPLIER: 2
  }
} as const;

// 策略参数
export const STRATEGY_PARAMS = {
  TREND_FOLLOWING: {
    TIMEFRAME: TimeFrame.H4,
    ATR_MULTIPLIER: 0.25,
    MIN_SCORE: 60
  },
  MEAN_REVERSION: {
    TIMEFRAME: TimeFrame.M15,
    ATR_MULTIPLIER: 1.0,
    MIN_SCORE: 70,
    BANDWIDTH_THRESHOLD: 0.04 // 4%
  }
} as const;

// API端点
export const API_ENDPOINTS = {
  ANALYZE: '/api/analyze-now',
  BACKTEST: '/api/backtest',
  BACKTEST_STATUS: '/api/backtest/:jobId/status',
  BACKTEST_RESULTS: '/api/backtest/:jobId/results',
  SIGNALS: '/api/signals',
  CONFIG: '/api/config',
  HEALTH: '/health'
} as const;

// WebSocket事件
export const WS_EVENTS = {
  SIGNAL_CREATED: 'signal:created',
  BACKTEST_PROGRESS: 'backtest:progress',
  BACKTEST_COMPLETED: 'backtest:completed',
  MARKET_DATA_UPDATE: 'market:update'
} as const;
