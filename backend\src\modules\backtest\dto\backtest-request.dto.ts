import { IsEnum, IsNotEmpty, IsDateString, <PERSON><PERSON><PERSON>ber, Min } from 'class-validator';
import { Transform } from 'class-transformer';
import { TradingPair, StrategyType } from '@signal-dash/shared';

export class BacktestRequestDto {
  @IsNotEmpty()
  @IsEnum(['BTC/USDT', 'ETH/USDT', 'SOL/USDT'])
  asset: TradingPair;

  @IsNotEmpty()
  @IsEnum(StrategyType)
  strategy: StrategyType;

  @IsNotEmpty()
  @IsDateString()
  startDate: string;

  @IsNotEmpty()
  @IsDateString()
  endDate: string;

  @IsNotEmpty()
  @IsNumber()
  @Min(100)
  @Transform(({ value }) => parseFloat(value))
  initialCapital: number;
}
