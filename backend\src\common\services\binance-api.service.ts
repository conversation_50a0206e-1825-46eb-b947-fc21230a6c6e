import { Injectable, HttpException, HttpStatus } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios, { AxiosInstance } from 'axios';
import { Kline, TradingPair, TimeFrame } from '@signal-dash/shared';
import { toBinanceSymbol, retry } from '@signal-dash/shared';

/**
 * 币安API响应接口
 */
interface BinanceKlineResponse {
  0: number;  // 开盘时间
  1: string;  // 开盘价
  2: string;  // 最高价
  3: string;  // 最低价
  4: string;  // 收盘价
  5: string;  // 成交量
  6: number;  // 收盘时间
  7: string;  // 成交额
  8: number;  // 成交笔数
  9: string;  // 主动买入成交量
  10: string; // 主动买入成交额
  11: string; // 忽略此参数
}

@Injectable()
export class BinanceApiService {
  private readonly httpClient: AxiosInstance;
  private readonly baseUrl: string;
  
  constructor(private configService: ConfigService) {
    this.baseUrl = this.configService.get<string>('BINANCE_API_URL', 'https://api.binance.com');
    
    this.httpClient = axios.create({
      baseURL: this.baseUrl,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    // 请求拦截器
    this.httpClient.interceptors.request.use(
      (config) => {
        console.log(`币安API请求: ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      (error) => Promise.reject(error)
    );
    
    // 响应拦截器
    this.httpClient.interceptors.response.use(
      (response) => response,
      (error) => {
        console.error('币安API请求失败:', error.response?.data || error.message);
        return Promise.reject(error);
      }
    );
  }
  
  /**
   * 获取K线数据
   */
  async getKlines(
    pair: TradingPair,
    timeFrame: TimeFrame,
    limit: number = 500,
    startTime?: number,
    endTime?: number
  ): Promise<Kline[]> {
    try {
      const symbol = toBinanceSymbol(pair);
      const params: any = {
        symbol,
        interval: timeFrame,
        limit: Math.min(limit, 1000), // 币安限制最大1000
      };
      
      if (startTime) params.startTime = startTime;
      if (endTime) params.endTime = endTime;
      
      const response = await retry(async () => {
        return this.httpClient.get<BinanceKlineResponse[]>('/api/v3/klines', { params });
      }, 3, 1000);
      
      return response.data.map(this.transformKlineData);
    } catch (error) {
      throw new HttpException(
        `获取${pair}的K线数据失败: ${error.message}`,
        HttpStatus.BAD_REQUEST
      );
    }
  }
  
  /**
   * 获取最新价格
   */
  async getLatestPrice(pair: TradingPair): Promise<number> {
    try {
      const symbol = toBinanceSymbol(pair);
      
      const response = await retry(async () => {
        return this.httpClient.get<{ price: string }>('/api/v3/ticker/price', {
          params: { symbol }
        });
      }, 3, 1000);
      
      return parseFloat(response.data.price);
    } catch (error) {
      throw new HttpException(
        `获取${pair}最新价格失败: ${error.message}`,
        HttpStatus.BAD_REQUEST
      );
    }
  }
  
  /**
   * 获取24小时价格变化统计
   */
  async get24hrStats(pair: TradingPair): Promise<{
    priceChange: number;
    priceChangePercent: number;
    volume: number;
    high: number;
    low: number;
  }> {
    try {
      const symbol = toBinanceSymbol(pair);
      
      const response = await retry(async () => {
        return this.httpClient.get('/api/v3/ticker/24hr', {
          params: { symbol }
        });
      }, 3, 1000);
      
      const data = response.data;
      return {
        priceChange: parseFloat(data.priceChange),
        priceChangePercent: parseFloat(data.priceChangePercent),
        volume: parseFloat(data.volume),
        high: parseFloat(data.highPrice),
        low: parseFloat(data.lowPrice),
      };
    } catch (error) {
      throw new HttpException(
        `获取${pair}的24小时统计失败: ${error.message}`,
        HttpStatus.BAD_REQUEST
      );
    }
  }
  
  /**
   * 检查服务器连接状态
   */
  async ping(): Promise<boolean> {
    try {
      await this.httpClient.get('/api/v3/ping');
      return true;
    } catch (error) {
      console.error('币安API连接失败:', error.message);
      return false;
    }
  }
  
  /**
   * 获取服务器时间
   */
  async getServerTime(): Promise<number> {
    try {
      const response = await this.httpClient.get<{ serverTime: number }>('/api/v3/time');
      return response.data.serverTime;
    } catch (error) {
      throw new HttpException(
        `获取服务器时间失败: ${error.message}`,
        HttpStatus.BAD_REQUEST
      );
    }
  }
  
  /**
   * 转换币安K线数据格式
   */
  private transformKlineData(binanceKline: BinanceKlineResponse): Kline {
    return {
      timestamp: binanceKline[0],
      open: parseFloat(binanceKline[1]),
      high: parseFloat(binanceKline[2]),
      low: parseFloat(binanceKline[3]),
      close: parseFloat(binanceKline[4]),
      volume: parseFloat(binanceKline[5]),
    };
  }
}
