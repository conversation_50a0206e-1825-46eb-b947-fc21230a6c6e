import { Kline, TradingSignal, StrategyType, SignalType, TradingPair, TimeFrame } from '@signal-dash/shared';
import { TechnicalAnalysisService } from '../services/technical-analysis.service';

/**
 * 策略分析结果
 */
export interface StrategyAnalysisResult {
  signal: SignalType;
  score: number;
  reasoning: string[];
  stopLoss?: number;
  takeProfit?: number;
}

/**
 * 交易策略基类
 */
export abstract class BaseStrategy {
  protected technicalAnalysis: TechnicalAnalysisService;
  
  constructor(
    protected strategyType: StrategyType,
    protected timeFrame: TimeFrame
  ) {
    this.technicalAnalysis = new TechnicalAnalysisService();
  }
  
  /**
   * 分析市场并生成信号
   */
  abstract analyze(klines: Kline[], asset: TradingPair): StrategyAnalysisResult;
  
  /**
   * 生成完整的交易信号
   */
  generateSignal(klines: Kline[], asset: TradingPair): TradingSignal | null {
    const indicators = this.technicalAnalysis.calculateIndicators(klines);
    if (!indicators) {
      return null;
    }
    
    const analysis = this.analyze(klines, asset);
    const currentPrice = klines[klines.length - 1].close;
    
    return {
      id: this.generateSignalId(),
      timestamp: Date.now(),
      asset,
      strategy: this.strategyType,
      timeFrame: this.timeFrame,
      signalType: analysis.signal,
      score: analysis.score,
      price: currentPrice,
      stopLoss: analysis.stopLoss,
      takeProfit: analysis.takeProfit,
      reasoning: analysis.reasoning,
      indicators,
    };
  }
  
  /**
   * 生成信号ID
   */
  protected generateSignalId(): string {
    return `${this.strategyType}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
  
  /**
   * 计算评分
   */
  protected calculateScore(factors: { [key: string]: number }): number {
    const totalScore = Object.values(factors).reduce((sum, score) => sum + score, 0);
    return Math.max(0, Math.min(100, totalScore));
  }
  
  /**
   * 添加评分原因
   */
  protected addReasoning(reasoning: string[], factor: string, score: number): void {
    const sign = score >= 0 ? '+' : '';
    reasoning.push(`${factor}: ${sign}${score}分`);
  }
}
