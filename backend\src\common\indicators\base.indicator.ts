import { Kline } from '@signal-dash/shared';

/**
 * 技术指标基类
 */
export abstract class BaseIndicator {
  protected data: Kline[] = [];
  
  constructor(protected period: number) {}
  
  /**
   * 更新数据
   */
  update(klines: Kline[]): void {
    this.data = klines;
  }
  
  /**
   * 计算指标值
   */
  abstract calculate(): any;
  
  /**
   * 获取收盘价数组
   */
  protected getClosePrices(): number[] {
    return this.data.map(k => k.close);
  }
  
  /**
   * 获取最高价数组
   */
  protected getHighPrices(): number[] {
    return this.data.map(k => k.high);
  }
  
  /**
   * 获取最低价数组
   */
  protected getLowPrices(): number[] {
    return this.data.map(k => k.low);
  }
  
  /**
   * 获取成交量数组
   */
  protected getVolumes(): number[] {
    return this.data.map(k => k.volume);
  }
  
  /**
   * 计算简单移动平均
   */
  protected sma(values: number[], period: number): number[] {
    const result: number[] = [];
    
    for (let i = period - 1; i < values.length; i++) {
      const sum = values.slice(i - period + 1, i + 1).reduce((a, b) => a + b, 0);
      result.push(sum / period);
    }
    
    return result;
  }
  
  /**
   * 计算标准差
   */
  protected standardDeviation(values: number[], period: number): number[] {
    const result: number[] = [];
    const smaValues = this.sma(values, period);
    
    for (let i = 0; i < smaValues.length; i++) {
      const dataIndex = i + period - 1;
      const slice = values.slice(dataIndex - period + 1, dataIndex + 1);
      const mean = smaValues[i];
      const variance = slice.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / period;
      result.push(Math.sqrt(variance));
    }
    
    return result;
  }
}
