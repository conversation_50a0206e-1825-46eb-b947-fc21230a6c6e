import { Controller, Post, Get, Delete, Body, Param } from '@nestjs/common';
import { BacktestMemoryService } from './backtest-memory.service';

@Controller('backtest')
export class BacktestMemoryController {
  constructor(private readonly backtestService: BacktestMemoryService) {}

  @Post()
  async createBacktest(@Body() config: any) {
    return await this.backtestService.createBacktest(config);
  }

  @Get()
  async getBacktests() {
    return await this.backtestService.getBacktests();
  }

  @Get(':id')
  async getBacktest(@Param('id') id: string) {
    return await this.backtestService.getBacktest(id);
  }

  @Delete(':id')
  async deleteBacktest(@Param('id') id: string) {
    const deleted = await this.backtestService.deleteBacktest(id);
    return { success: deleted };
  }
}
