import { Injectable } from '@nestjs/common';
import { 
  GridTradingConfig, 
  GridTradingPosition, 
  GridTradingOrder, 
  GridTradingPerformance,
  CreateGridConfigDto,
  UpdateGridConfigDto
} from '@signal-dash/shared';

@Injectable()
export class GridTradingMemoryService {
  private configs: Map<string, GridTradingConfig> = new Map();
  private positions: Map<string, GridTradingPosition[]> = new Map();
  private orders: Map<string, GridTradingOrder[]> = new Map();
  private performance: Map<string, GridTradingPerformance[]> = new Map();

  // 配置管理
  async createConfig(dto: CreateGridConfigDto): Promise<GridTradingConfig> {
    const config: GridTradingConfig = {
      id: this.generateId(),
      symbol: dto.symbol,
      baseAmount: dto.baseAmount,
      gridCount: dto.gridCount,
      priceRange: dto.priceRange,
      isActive: false,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    
    this.configs.set(config.id, config);
    this.positions.set(config.id, []);
    this.orders.set(config.id, []);
    this.performance.set(config.id, []);
    
    return config;
  }

  async getConfigs(): Promise<GridTradingConfig[]> {
    return Array.from(this.configs.values());
  }

  async getConfig(id: string): Promise<GridTradingConfig | null> {
    return this.configs.get(id) || null;
  }

  async updateConfig(id: string, dto: UpdateGridConfigDto): Promise<GridTradingConfig | null> {
    const config = this.configs.get(id);
    if (!config) return null;

    const updatedConfig = {
      ...config,
      ...dto,
      updatedAt: new Date(),
    };
    
    this.configs.set(id, updatedConfig);
    return updatedConfig;
  }

  async deleteConfig(id: string): Promise<boolean> {
    const deleted = this.configs.delete(id);
    if (deleted) {
      this.positions.delete(id);
      this.orders.delete(id);
      this.performance.delete(id);
    }
    return deleted;
  }

  // 持仓管理
  async getPositions(configId: string): Promise<GridTradingPosition[]> {
    return this.positions.get(configId) || [];
  }

  async addPosition(configId: string, position: Omit<GridTradingPosition, 'id' | 'createdAt'>): Promise<GridTradingPosition> {
    const newPosition: GridTradingPosition = {
      ...position,
      id: this.generateId(),
      createdAt: new Date(),
    };
    
    const positions = this.positions.get(configId) || [];
    positions.push(newPosition);
    this.positions.set(configId, positions);
    
    return newPosition;
  }

  // 订单管理
  async getOrders(configId: string): Promise<GridTradingOrder[]> {
    return this.orders.get(configId) || [];
  }

  async addOrder(configId: string, order: Omit<GridTradingOrder, 'id' | 'createdAt'>): Promise<GridTradingOrder> {
    const newOrder: GridTradingOrder = {
      ...order,
      id: this.generateId(),
      createdAt: new Date(),
    };
    
    const orders = this.orders.get(configId) || [];
    orders.push(newOrder);
    this.orders.set(configId, orders);
    
    return newOrder;
  }

  // 性能数据管理
  async getPerformance(configId: string): Promise<GridTradingPerformance[]> {
    return this.performance.get(configId) || [];
  }

  async addPerformanceRecord(configId: string, record: Omit<GridTradingPerformance, 'id' | 'timestamp'>): Promise<GridTradingPerformance> {
    const newRecord: GridTradingPerformance = {
      ...record,
      id: this.generateId(),
      timestamp: new Date(),
    };
    
    const records = this.performance.get(configId) || [];
    records.push(newRecord);
    this.performance.set(configId, records);
    
    return newRecord;
  }

  // 启动/停止网格
  async startGrid(id: string): Promise<boolean> {
    const config = this.configs.get(id);
    if (!config) return false;

    config.isActive = true;
    config.updatedAt = new Date();
    this.configs.set(id, config);
    
    // 模拟创建初始订单
    await this.createInitialOrders(id, config);
    
    return true;
  }

  async stopGrid(id: string): Promise<boolean> {
    const config = this.configs.get(id);
    if (!config) return false;

    config.isActive = false;
    config.updatedAt = new Date();
    this.configs.set(id, config);
    
    return true;
  }

  // 私有方法
  private generateId(): string {
    return Math.random().toString(36).substr(2, 9);
  }

  private async createInitialOrders(configId: string, config: GridTradingConfig): Promise<void> {
    // 模拟创建网格订单
    const { priceRange, gridCount } = config;
    const priceStep = (priceRange.max - priceRange.min) / gridCount;
    
    for (let i = 0; i < gridCount; i++) {
      const price = priceRange.min + (i * priceStep);
      
      // 创建买单
      await this.addOrder(configId, {
        configId,
        type: 'buy',
        price,
        quantity: config.baseAmount / price,
        status: 'pending',
        gridLevel: i,
      });
      
      // 创建卖单
      if (i > 0) {
        await this.addOrder(configId, {
          configId,
          type: 'sell',
          price: price + priceStep,
          quantity: config.baseAmount / price,
          status: 'pending',
          gridLevel: i,
        });
      }
    }
  }
}
