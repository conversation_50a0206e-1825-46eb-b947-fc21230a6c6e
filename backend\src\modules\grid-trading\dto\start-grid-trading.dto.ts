import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>al,
  IsBoolean,
  Min,
  Max,
  IsPositive,
  IsObject,
} from 'class-validator';
import { Transform } from 'class-transformer';

export class StartGridTradingDto {
  @IsString()
  symbol: string;

  @IsNumber()
  @IsPositive()
  @Min(0.1)
  @Max(10)
  @Transform(({ value }) => parseFloat(value))
  gridSize: number;

  @IsNumber()
  @IsPositive()
  @Transform(({ value }) => parseFloat(value))
  initialCapital: number;

  @IsOptional()
  @IsNumber()
  @IsPositive()
  @Transform(({ value }) => value ? parseFloat(value) : undefined)
  basePrice?: number;

  @IsOptional()
  @IsNumber()
  @Min(0.1)
  @Max(5)
  @Transform(({ value }) => value ? parseFloat(value) : 1.0)
  minGridSize?: number = 1.0;

  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(10)
  @Transform(({ value }) => value ? parseFloat(value) : 4.0)
  maxGridSize?: number = 4.0;

  @IsOptional()
  @IsNumber()
  @Min(0.1)
  @Max(1)
  @Transform(({ value }) => value ? parseFloat(value) : 0.9)
  maxPositionRatio?: number = 0.9;

  @IsOptional()
  @IsNumber()
  @Min(0.01)
  @Max(0.5)
  @Transform(({ value }) => value ? parseFloat(value) : 0.1)
  minPositionRatio?: number = 0.1;

  @IsOptional()
  @IsBoolean()
  enableVolatilityAdjustment?: boolean = true;

  @IsOptional()
  @IsBoolean()
  enableRiskManagement?: boolean = true;

  @IsOptional()
  @IsBoolean()
  enableFundManagement?: boolean = false;

  @IsOptional()
  @IsNumber()
  @Min(7)
  @Max(365)
  @Transform(({ value }) => value ? parseInt(value) : 52)
  volatilityWindow?: number = 52;

  @IsOptional()
  @IsNumber()
  @Min(0.8)
  @Max(0.99)
  @Transform(({ value }) => value ? parseFloat(value) : 0.94)
  ewmaLambda?: number = 0.94;

  @IsOptional()
  @IsNumber()
  @Min(0.1)
  @Max(0.9)
  @Transform(({ value }) => value ? parseFloat(value) : 0.7)
  hybridWeight?: number = 0.7;

  @IsOptional()
  @IsNumber()
  @Min(300)
  @Max(86400)
  @Transform(({ value }) => value ? parseInt(value) : 3600)
  adjustmentInterval?: number = 3600;

  @IsOptional()
  @IsNumber()
  @IsPositive()
  @Transform(({ value }) => value ? parseFloat(value) : 20.0)
  minTradeAmount?: number = 20.0;

  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(10)
  @Transform(({ value }) => value ? parseInt(value) : 5)
  maxRetries?: number = 5;

  @IsOptional()
  @IsNumber()
  @Min(60)
  @Max(3600)
  @Transform(({ value }) => value ? parseInt(value) : 300)
  riskCheckInterval?: number = 300;

  @IsOptional()
  @IsObject()
  additionalParams?: Record<string, any>;
}
