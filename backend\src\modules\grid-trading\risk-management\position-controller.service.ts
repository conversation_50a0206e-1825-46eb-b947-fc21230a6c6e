import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { TradingPosition, PositionSide, PositionStatus } from '../entities/trading-position.entity';
import { GridTradingConfig } from '../entities/grid-trading-config.entity';
import { ExchangeClientService } from '../exchange/exchange-client.service';
import { OrderManagerService, OrderRequest } from '../exchange/order-manager.service';
import { RiskManagerService, RiskState } from './risk-manager.service';
import { PositionSummary } from '../interfaces/grid-trading.interface';

export interface PositionAction {
  type: 'open' | 'close' | 'adjust';
  side: 'buy' | 'sell';
  quantity: number;
  price: number;
  reason: string;
}

@Injectable()
export class PositionControllerService {
  private readonly logger = new Logger(PositionControllerService.name);

  constructor(
    @InjectRepository(TradingPosition)
    private readonly positionRepository: Repository<TradingPosition>,
    @InjectRepository(GridTradingConfig)
    private readonly configRepository: Repository<GridTradingConfig>,
    private readonly exchangeClient: ExchangeClientService,
    private readonly orderManager: OrderManagerService,
    private readonly riskManager: RiskManagerService,
  ) {}

  /**
   * 检查并执行仓位调整
   */
  async checkAndExecutePositionAdjustment(
    symbol: string,
    sessionId: string,
  ): Promise<PositionAction | null> {
    try {
      // 1. 获取配置和当前价格
      const config = await this.configRepository.findOne({ where: { symbol } });
      if (!config) {
        throw new Error(`未找到 ${symbol} 的配置`);
      }

      const currentPrice = await this.exchangeClient.getCurrentPrice(symbol);
      
      // 2. 风险检查
      const riskCheck = await this.riskManager.checkPositionLimits(symbol, sessionId);
      if (riskCheck.state === RiskState.BLOCK_ALL) {
        this.logger.warn(`${symbol} 风险状态阻止交易: ${riskCheck.warnings.join(', ')}`);
        return null;
      }

      // 3. 获取当前仓位
      const positions = await this.getActivePositions(symbol, sessionId);
      const positionSummary = await this.calculatePositionSummary(symbol, sessionId, currentPrice);

      // 4. 分析仓位需求
      const action = await this.analyzePositionRequirement(
        config,
        positionSummary,
        currentPrice,
        riskCheck.state,
      );

      // 5. 执行仓位调整
      if (action) {
        const success = await this.executePositionAction(action, symbol, sessionId);
        if (success) {
          this.logger.log(`仓位调整成功: ${symbol} ${action.type} ${action.side} ${action.quantity}`);
          return action;
        }
      }

      return null;
    } catch (error) {
      this.logger.error(`仓位调整检查失败 ${symbol}: ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * 获取仓位摘要
   */
  async getPositionSummary(symbol: string, sessionId: string): Promise<PositionSummary> {
    const currentPrice = await this.exchangeClient.getCurrentPrice(symbol);
    return this.calculatePositionSummary(symbol, sessionId, currentPrice);
  }

  /**
   * 强制平仓
   */
  async forceCloseAllPositions(symbol: string, sessionId: string): Promise<number> {
    this.logger.warn(`强制平仓所有 ${symbol} 仓位`);

    try {
      const openPositions = await this.positionRepository.find({
        where: {
          symbol,
          sessionId,
          status: PositionStatus.OPEN,
        },
      });

      let closedCount = 0;
      const currentPrice = await this.exchangeClient.getCurrentPrice(symbol);

      for (const position of openPositions) {
        try {
          const orderRequest: OrderRequest = {
            symbol,
            sessionId,
            side: position.side === PositionSide.LONG ? 'sell' : 'buy',
            price: currentPrice,
            quantity: position.remainingQuantity,
            positionId: position.id,
          };

          const result = await this.orderManager.executeOrder(orderRequest);
          if (result.success) {
            closedCount++;
            
            // 更新仓位状态
            position.status = PositionStatus.CLOSED;
            position.exitPrice = currentPrice;
            position.closedAt = new Date();
            await this.positionRepository.save(position);
          }
        } catch (error) {
          this.logger.error(`强制平仓失败 ${position.id}: ${error.message}`);
        }
      }

      this.logger.log(`强制平仓完成: ${closedCount}/${openPositions.length}`);
      return closedCount;
    } catch (error) {
      this.logger.error(`强制平仓失败: ${error.message}`);
      return 0;
    }
  }

  /**
   * 获取活跃仓位
   */
  async getActivePositions(symbol: string, sessionId: string): Promise<TradingPosition[]> {
    return this.positionRepository.find({
      where: {
        symbol,
        sessionId,
        status: PositionStatus.OPEN,
      },
      order: { createdAt: 'DESC' },
    });
  }

  /**
   * 计算仓位摘要
   */
  private async calculatePositionSummary(
    symbol: string,
    sessionId: string,
    currentPrice: number,
  ): Promise<PositionSummary> {
    const positions = await this.positionRepository.find({
      where: { symbol, sessionId },
    });

    const openPositions = positions.filter(p => p.status === PositionStatus.OPEN);
    const closedPositions = positions.filter(p => p.status === PositionStatus.CLOSED);

    // 计算总价值和盈亏
    let totalValue = 0;
    let unrealizedPnl = 0;
    let realizedPnl = 0;
    let totalEntryValue = 0;
    let totalQuantity = 0;

    for (const position of openPositions) {
      const positionValue = position.remainingQuantity * currentPrice;
      totalValue += positionValue;
      totalQuantity += position.remainingQuantity;
      
      const entryValue = position.remainingQuantity * position.entryPrice;
      totalEntryValue += entryValue;
      
      // 计算未实现盈亏
      const pnl = position.side === PositionSide.LONG 
        ? positionValue - entryValue
        : entryValue - positionValue;
      unrealizedPnl += pnl;
    }

    // 计算已实现盈亏
    realizedPnl = closedPositions.reduce((sum, p) => sum + p.realizedPnl, 0);

    // 计算平均入场价
    const averageEntryPrice = totalQuantity > 0 ? totalEntryValue / totalQuantity : 0;

    return {
      symbol,
      totalPositions: positions.length,
      openPositions: openPositions.length,
      closedPositions: closedPositions.length,
      totalValue,
      unrealizedPnl,
      realizedPnl,
      averageEntryPrice,
      currentPrice,
    };
  }

  /**
   * 分析仓位需求
   */
  private async analyzePositionRequirement(
    config: GridTradingConfig,
    positionSummary: PositionSummary,
    currentPrice: number,
    riskState: RiskState,
  ): Promise<PositionAction | null> {
    // 计算目标仓位价值（初始资金的一定比例）
    const targetPositionValue = config.initialCapital * 0.1; // 10%
    const currentPositionValue = positionSummary.totalValue;
    const valueDifference = targetPositionValue - currentPositionValue;

    // 如果差异小于最小交易金额，不执行调整
    if (Math.abs(valueDifference) < config.minTradeAmount) {
      return null;
    }

    // 根据风险状态和价值差异确定操作
    if (valueDifference > 0 && riskState !== RiskState.ALLOW_SELL_ONLY) {
      // 需要增加仓位（买入）
      return {
        type: 'open',
        side: 'buy',
        quantity: valueDifference / currentPrice,
        price: currentPrice,
        reason: `仓位不足，需要增加 ${valueDifference.toFixed(2)} 价值的仓位`,
      };
    } else if (valueDifference < 0 && riskState !== RiskState.ALLOW_BUY_ONLY) {
      // 需要减少仓位（卖出）
      return {
        type: 'close',
        side: 'sell',
        quantity: Math.abs(valueDifference) / currentPrice,
        price: currentPrice,
        reason: `仓位过多，需要减少 ${Math.abs(valueDifference).toFixed(2)} 价值的仓位`,
      };
    }

    return null;
  }

  /**
   * 执行仓位操作
   */
  private async executePositionAction(
    action: PositionAction,
    symbol: string,
    sessionId: string,
  ): Promise<boolean> {
    try {
      // 获取交易对信息以调整精度
      const adjustedQuantity = await this.exchangeClient.calculateOrderAmount(
        symbol,
        action.quantity * action.price,
        action.price,
      );

      const orderRequest: OrderRequest = {
        symbol,
        sessionId,
        side: action.side,
        price: action.price,
        quantity: adjustedQuantity,
      };

      // 如果是平仓操作，需要关联现有仓位
      if (action.type === 'close') {
        const openPosition = await this.positionRepository.findOne({
          where: {
            symbol,
            sessionId,
            status: PositionStatus.OPEN,
          },
          order: { createdAt: 'ASC' }, // 先进先出
        });

        if (openPosition) {
          orderRequest.positionId = openPosition.id;
        }
      }

      const result = await this.orderManager.executeOrder(orderRequest);
      
      if (result.success) {
        this.logger.log(`仓位操作成功: ${action.reason}`);
        return true;
      } else {
        this.logger.error(`仓位操作失败: ${result.error}`);
        
        // 记录失败到风险管理器
        await this.riskManager.recordTradingFailure(
          symbol,
          sessionId,
          result.error || '未知错误',
        );
        
        return false;
      }
    } catch (error) {
      this.logger.error(`执行仓位操作失败: ${error.message}`, error.stack);
      
      await this.riskManager.recordTradingFailure(
        symbol,
        sessionId,
        error.message,
      );
      
      return false;
    }
  }

  /**
   * 检查仓位健康度
   */
  async checkPositionHealth(symbol: string, sessionId: string): Promise<{
    isHealthy: boolean;
    issues: string[];
    suggestions: string[];
  }> {
    const issues: string[] = [];
    const suggestions: string[] = [];

    try {
      const positionSummary = await this.getPositionSummary(symbol, sessionId);
      const config = await this.configRepository.findOne({ where: { symbol } });

      if (!config) {
        issues.push('未找到交易配置');
        return { isHealthy: false, issues, suggestions };
      }

      // 检查未实现盈亏比例
      const pnlRatio = positionSummary.totalValue > 0 
        ? positionSummary.unrealizedPnl / positionSummary.totalValue 
        : 0;

      if (pnlRatio < -0.1) { // 亏损超过10%
        issues.push(`未实现亏损过大: ${(pnlRatio * 100).toFixed(2)}%`);
        suggestions.push('考虑调整网格参数或止损');
      }

      // 检查仓位集中度
      if (positionSummary.openPositions > 20) {
        issues.push(`开仓数量过多: ${positionSummary.openPositions}`);
        suggestions.push('考虑合并部分仓位');
      }

      // 检查平均持仓成本与当前价格的偏离
      const priceDeviation = Math.abs(positionSummary.currentPrice - positionSummary.averageEntryPrice) 
        / positionSummary.averageEntryPrice;

      if (priceDeviation > 0.2) { // 偏离超过20%
        issues.push(`持仓成本偏离过大: ${(priceDeviation * 100).toFixed(2)}%`);
        suggestions.push('考虑调整基准价格');
      }

      const isHealthy = issues.length === 0;
      return { isHealthy, issues, suggestions };

    } catch (error) {
      this.logger.error(`仓位健康检查失败: ${error.message}`);
      return {
        isHealthy: false,
        issues: [`健康检查失败: ${error.message}`],
        suggestions: ['请检查系统状态'],
      };
    }
  }
}
