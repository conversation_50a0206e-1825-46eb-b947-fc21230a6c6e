import { BaseIndicator } from './base.indicator';

/**
 * 平均真实波幅 (ATR)
 */
export class ATRIndicator extends BaseIndicator {
  constructor(period: number = 14) {
    super(period);
  }
  
  /**
   * 计算ATR值
   */
  calculate(): number[] {
    if (this.data.length < this.period + 1) {
      return [];
    }
    
    const trueRanges: number[] = [];
    
    // 计算真实波幅 (True Range)
    for (let i = 1; i < this.data.length; i++) {
      const current = this.data[i];
      const previous = this.data[i - 1];
      
      const tr1 = current.high - current.low;
      const tr2 = Math.abs(current.high - previous.close);
      const tr3 = Math.abs(current.low - previous.close);
      
      const trueRange = Math.max(tr1, tr2, tr3);
      trueRanges.push(trueRange);
    }
    
    if (trueRanges.length < this.period) {
      return [];
    }
    
    const result: number[] = [];
    
    // 第一个ATR值使用SMA
    const firstATR = trueRanges.slice(0, this.period).reduce((a, b) => a + b, 0) / this.period;
    result.push(firstATR);
    
    // 后续ATR值使用Wilder's smoothing: ATR = ((ATR_prev * (period - 1)) + TR) / period
    for (let i = this.period; i < trueRanges.length; i++) {
      const atr = ((result[result.length - 1] * (this.period - 1)) + trueRanges[i]) / this.period;
      result.push(atr);
    }
    
    return result;
  }
  
  /**
   * 获取最新ATR值
   */
  getLatest(): number | null {
    const atrValues = this.calculate();
    return atrValues.length > 0 ? atrValues[atrValues.length - 1] : null;
  }
  
  /**
   * 计算止损距离
   */
  getStopLossDistance(multiplier: number = 1.0): number | null {
    const atr = this.getLatest();
    return atr !== null ? atr * multiplier : null;
  }
}
