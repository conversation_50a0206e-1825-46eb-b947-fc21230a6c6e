import { Controller, Get, Query, Param, Delete, HttpException, HttpStatus } from '@nestjs/common';
import { SignalService } from './signal.service';
import { TradingSignal, TradingPair, StrategyType } from '@signal-dash/shared';

@Controller('signals')
export class SignalController {
  constructor(private readonly signalService: SignalService) {}

  @Get()
  async getSignals(
    @Query('asset') asset?: TradingPair,
    @Query('strategy') strategy?: StrategyType,
    @Query('limit') limit?: string,
    @Query('offset') offset?: string
  ): Promise<{ signals: TradingSignal[]; total: number }> {
    try {
      const limitNum = limit ? parseInt(limit, 10) : 50;
      const offsetNum = offset ? parseInt(offset, 10) : 0;
      
      return await this.signalService.getSignals({
        asset,
        strategy,
        limit: limitNum,
        offset: offsetNum
      });
    } catch (error) {
      throw new HttpException(
        `获取信号失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get(':id')
  async getSignalById(@Param('id') id: string): Promise<TradingSignal> {
    try {
      const signal = await this.signalService.getSignalById(id);
      if (!signal) {
        throw new HttpException('信号不存在', HttpStatus.NOT_FOUND);
      }
      return signal;
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        `获取信号失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Delete(':id')
  async deleteSignal(@Param('id') id: string): Promise<{ success: boolean }> {
    try {
      await this.signalService.deleteSignal(id);
      return { success: true };
    } catch (error) {
      throw new HttpException(
        `删除信号失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('stats/summary')
  async getSignalStats(): Promise<{
    totalSignals: number;
    todaySignals: number;
    signalsByAsset: { [asset: string]: number };
    signalsByStrategy: { [strategy: string]: number };
  }> {
    try {
      return await this.signalService.getSignalStats();
    } catch (error) {
      throw new HttpException(
        `获取信号统计失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }
}
