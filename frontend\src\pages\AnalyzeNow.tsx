import { useState } from 'react';
import { useMutation } from '@tanstack/react-query';
import { TradingPair, StrategyType, AnalyzeRequest, SignalType } from '@signal-dash/shared';
import { api } from '../services/api';
import { Loader2, TrendingUp, TrendingDown, Minus } from 'lucide-react';

export function AnalyzeNow() {
  const [selectedAsset, setSelectedAsset] = useState<TradingPair>('BTC/USDT');
  const [selectedStrategy, setSelectedStrategy] = useState<StrategyType>(StrategyType.TREND_FOLLOWING);

  const analyzeMutation = useMutation({
    mutationFn: (request: AnalyzeRequest) => api.analyzeNow(request),
    onError: (error) => {
      console.error('分析失败:', error);
    },
  });

  const handleAnalyze = () => {
    analyzeMutation.mutate({
      asset: selectedAsset,
      strategy: selectedStrategy,
    });
  };

  const getSignalIcon = (signalType: SignalType) => {
    switch (signalType) {
      case SignalType.BUY:
        return <TrendingUp className="h-8 w-8 text-green-600" />;
      case SignalType.SELL:
        return <TrendingDown className="h-8 w-8 text-red-600" />;
      default:
        return <Minus className="h-8 w-8 text-gray-600" />;
    }
  };

  const getSignalColor = (signalType: SignalType) => {
    switch (signalType) {
      case SignalType.BUY:
        return 'text-green-600 bg-green-50 border-green-200';
      case SignalType.SELL:
        return 'text-red-600 bg-red-50 border-red-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <div>
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">手动分析</h1>
        <p className="mt-2 text-gray-600">
          即时分析当前市场状况，获取交易建议
        </p>
      </div>

      <div className="bg-white rounded-lg shadow p-6">
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
          {/* 输入区域 */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">分析参数</h3>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  交易对
                </label>
                <select
                  value={selectedAsset}
                  onChange={(e) => setSelectedAsset(e.target.value as TradingPair)}
                  className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                >
                  <option value="BTC/USDT">BTC/USDT</option>
                  <option value="ETH/USDT">ETH/USDT</option>
                  <option value="SOL/USDT">SOL/USDT</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  策略模型
                </label>
                <div className="space-y-2">
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="strategy"
                      value={StrategyType.TREND_FOLLOWING}
                      checked={selectedStrategy === StrategyType.TREND_FOLLOWING}
                      onChange={(e) => setSelectedStrategy(e.target.value as StrategyType)}
                      className="mr-2"
                    />
                    趋势跟踪策略 (H4)
                  </label>
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="strategy"
                      value={StrategyType.MEAN_REVERSION}
                      checked={selectedStrategy === StrategyType.MEAN_REVERSION}
                      onChange={(e) => setSelectedStrategy(e.target.value as StrategyType)}
                      className="mr-2"
                    />
                    震荡区间策略 (M15)
                  </label>
                </div>
              </div>

              <button
                onClick={handleAnalyze}
                disabled={analyzeMutation.isPending}
                className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
              >
                {analyzeMutation.isPending ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    分析中...
                  </>
                ) : (
                  '立即分析'
                )}
              </button>
            </div>
          </div>

          {/* 结果区域 */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">分析结果</h3>

            {analyzeMutation.isPending ? (
              <div className="text-center text-gray-500 py-8">
                <Loader2 className="h-8 w-8 mx-auto mb-4 animate-spin" />
                正在分析市场数据...
              </div>
            ) : analyzeMutation.error ? (
              <div className="text-center text-red-500 py-8">
                <p>分析失败</p>
                <p className="text-sm mt-2">{analyzeMutation.error.message}</p>
              </div>
            ) : analyzeMutation.data ? (
              <div className="space-y-4">
                {/* 信号卡片 */}
                <div className={`p-4 rounded-lg border-2 ${getSignalColor(analyzeMutation.data.signal.signalType)}`}>
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-3">
                      {getSignalIcon(analyzeMutation.data.signal.signalType)}
                      <div>
                        <h4 className="font-semibold text-lg">
                          {analyzeMutation.data.signal.signalType === SignalType.BUY ? '建议买入' :
                           analyzeMutation.data.signal.signalType === SignalType.SELL ? '建议卖出' : '建议观望'}
                        </h4>
                        <p className="text-sm opacity-75">{selectedAsset}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className={`text-2xl font-bold ${getScoreColor(analyzeMutation.data.signal.score)}`}>
                        {analyzeMutation.data.signal.score}
                      </p>
                      <p className="text-sm opacity-75">评分</p>
                    </div>
                  </div>

                  {/* 价格信息 */}
                  <div className="grid grid-cols-3 gap-4 text-sm">
                    <div>
                      <p className="opacity-75">当前价格</p>
                      <p className="font-medium">{analyzeMutation.data.signal.price.toFixed(4)}</p>
                    </div>
                    {analyzeMutation.data.signal.stopLoss && (
                      <div>
                        <p className="opacity-75">止损价</p>
                        <p className="font-medium">{analyzeMutation.data.signal.stopLoss.toFixed(4)}</p>
                      </div>
                    )}
                    {analyzeMutation.data.signal.takeProfit && (
                      <div>
                        <p className="opacity-75">止盈价</p>
                        <p className="font-medium">{analyzeMutation.data.signal.takeProfit.toFixed(4)}</p>
                      </div>
                    )}
                  </div>
                </div>

                {/* 分析依据 */}
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h5 className="font-medium text-gray-900 mb-2">分析依据</h5>
                  <ul className="space-y-1 text-sm text-gray-600">
                    {analyzeMutation.data.signal.reasoning.map((reason, index) => (
                      <li key={index} className="flex items-start">
                        <span className="mr-2">•</span>
                        <span>{reason}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                {/* 置信度 */}
                <div className="bg-blue-50 p-4 rounded-lg">
                  <div className="flex items-center justify-between">
                    <span className="font-medium text-blue-900">置信度</span>
                    <span className="text-blue-700 font-semibold">
                      {(analyzeMutation.data.confidence * 100).toFixed(1)}%
                    </span>
                  </div>
                  <div className="mt-2 bg-blue-200 rounded-full h-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${analyzeMutation.data.confidence * 100}%` }}
                    />
                  </div>
                </div>
              </div>
            ) : (
              <div className="text-center text-gray-500 py-8">
                请选择参数并点击"立即分析"
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
