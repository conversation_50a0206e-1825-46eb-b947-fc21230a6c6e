import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Cron, CronExpression } from '@nestjs/schedule';
import { RiskEvent, RiskEventType, RiskLevel } from '../entities/risk-event.entity';
import { GridTradingConfig } from '../entities/grid-trading-config.entity';
import { TradingPosition } from '../entities/trading-position.entity';
import { OrderHistory, OrderStatus } from '../entities/order-history.entity';
import { ExchangeClientService } from '../exchange/exchange-client.service';
import { RiskMetrics } from '../interfaces/grid-trading.interface';

export enum RiskState {
  ALLOW_ALL = 'allow_all',
  ALLOW_BUY_ONLY = 'allow_buy_only',
  ALLOW_SELL_ONLY = 'allow_sell_only',
  BLOCK_ALL = 'block_all',
}

export interface RiskCheckResult {
  state: RiskState;
  metrics: RiskMetrics;
  warnings: string[];
  actions: string[];
}

@Injectable()
export class RiskManagerService {
  private readonly logger = new Logger(RiskManagerService.name);
  
  // 风险状态缓存
  private riskStates = new Map<string, {
    state: RiskState;
    lastCheck: Date;
    consecutiveFailures: number;
    warnings: string[];
  }>();

  constructor(
    @InjectRepository(RiskEvent)
    private readonly riskEventRepository: Repository<RiskEvent>,
    @InjectRepository(GridTradingConfig)
    private readonly configRepository: Repository<GridTradingConfig>,
    @InjectRepository(TradingPosition)
    private readonly positionRepository: Repository<TradingPosition>,
    @InjectRepository(OrderHistory)
    private readonly orderRepository: Repository<OrderHistory>,
    private readonly exchangeClient: ExchangeClientService,
  ) {}

  /**
   * 检查仓位限制
   */
  async checkPositionLimits(
    symbol: string,
    sessionId: string,
  ): Promise<RiskCheckResult> {
    try {
      const config = await this.configRepository.findOne({ where: { symbol } });
      if (!config) {
        throw new Error(`未找到 ${symbol} 的配置`);
      }

      // 获取当前仓位信息
      const positions = await this.positionRepository.find({
        where: { symbol, sessionId },
      });

      // 获取账户余额
      const balances = await this.exchangeClient.getBalance();
      const currentPrice = await this.exchangeClient.getCurrentPrice(symbol);

      // 计算当前仓位比例
      const totalValue = this.calculateTotalValue(balances, currentPrice, symbol);
      const positionValue = this.calculatePositionValue(positions, currentPrice);
      const currentPositionRatio = totalValue > 0 ? positionValue / totalValue : 0;

      // 检查连续失败次数
      const consecutiveFailures = await this.getConsecutiveFailures(symbol, sessionId);

      // 构建风险指标
      const metrics: RiskMetrics = {
        currentPositionRatio,
        maxPositionRatio: config.maxPositionRatio,
        consecutiveFailures,
        maxConsecutiveFailures: config.maxRetries,
        lastRiskCheck: new Date(),
        riskLevel: this.calculateRiskLevel(currentPositionRatio, consecutiveFailures, config),
        warnings: [],
      };

      // 确定风险状态
      const state = this.determineRiskState(metrics, config);
      const warnings = this.generateWarnings(metrics, config);
      const actions = this.generateActions(state, warnings);

      // 更新风险状态缓存
      this.riskStates.set(symbol, {
        state,
        lastCheck: new Date(),
        consecutiveFailures,
        warnings,
      });

      // 记录高风险事件
      if (metrics.riskLevel === 'high' || metrics.riskLevel === 'critical') {
        await this.recordRiskEvent(symbol, sessionId, metrics, warnings);
      }

      return {
        state,
        metrics: { ...metrics, warnings },
        warnings,
        actions,
      };

    } catch (error) {
      this.logger.error(`风险检查失败 ${symbol}: ${error.message}`, error.stack);
      
      // 发生错误时采用保守策略
      return {
        state: RiskState.BLOCK_ALL,
        metrics: {
          currentPositionRatio: 0,
          maxPositionRatio: 0,
          consecutiveFailures: 0,
          maxConsecutiveFailures: 0,
          lastRiskCheck: new Date(),
          riskLevel: 'critical',
          warnings: [`风险检查失败: ${error.message}`],
        },
        warnings: [`风险检查失败: ${error.message}`],
        actions: ['暂停所有交易'],
      };
    }
  }

  /**
   * 检查波动率风险
   */
  async checkVolatilityRisk(
    symbol: string,
    currentVolatility: number,
    config: GridTradingConfig,
  ): Promise<boolean> {
    const volatilityThreshold = 0.5; // 50%年化波动率阈值
    
    if (currentVolatility > volatilityThreshold) {
      await this.recordRiskEvent(
        symbol,
        '', // sessionId可能为空
        {
          currentPositionRatio: 0,
          maxPositionRatio: 0,
          consecutiveFailures: 0,
          maxConsecutiveFailures: 0,
          lastRiskCheck: new Date(),
          riskLevel: 'high',
          warnings: [],
        },
        [`波动率过高: ${(currentVolatility * 100).toFixed(2)}%`],
        RiskEventType.VOLATILITY_SPIKE,
      );
      
      return true; // 存在风险
    }
    
    return false; // 无风险
  }

  /**
   * 检查余额风险
   */
  async checkBalanceRisk(
    symbol: string,
    requiredAmount: number,
    asset: string,
  ): Promise<boolean> {
    try {
      const balances = await this.exchangeClient.getBalance();
      const assetBalance = balances.find(b => b.asset === asset);
      
      if (!assetBalance || assetBalance.free < requiredAmount) {
        await this.recordRiskEvent(
          symbol,
          '',
          {
            currentPositionRatio: 0,
            maxPositionRatio: 0,
            consecutiveFailures: 0,
            maxConsecutiveFailures: 0,
            lastRiskCheck: new Date(),
            riskLevel: 'medium',
            warnings: [],
          },
          [`余额不足: 需要 ${requiredAmount} ${asset}, 可用 ${assetBalance?.free || 0}`],
          RiskEventType.BALANCE_INSUFFICIENT,
        );
        
        return true; // 存在风险
      }
      
      return false; // 无风险
    } catch (error) {
      this.logger.error(`余额风险检查失败: ${error.message}`);
      return true; // 出错时认为有风险
    }
  }

  /**
   * 记录交易失败
   */
  async recordTradingFailure(
    symbol: string,
    sessionId: string,
    error: string,
  ): Promise<void> {
    const riskState = this.riskStates.get(symbol);
    if (riskState) {
      riskState.consecutiveFailures++;
    }

    await this.recordRiskEvent(
      symbol,
      sessionId,
      {
        currentPositionRatio: 0,
        maxPositionRatio: 0,
        consecutiveFailures: riskState?.consecutiveFailures || 1,
        maxConsecutiveFailures: 5,
        lastRiskCheck: new Date(),
        riskLevel: 'medium',
        warnings: [],
      },
      [`交易失败: ${error}`],
      RiskEventType.API_ERROR,
    );
  }

  /**
   * 重置失败计数
   */
  resetFailureCount(symbol: string): void {
    const riskState = this.riskStates.get(symbol);
    if (riskState) {
      riskState.consecutiveFailures = 0;
    }
  }

  /**
   * 定时风险检查
   */
  @Cron(CronExpression.EVERY_5_MINUTES)
  async performScheduledRiskCheck(): Promise<void> {
    this.logger.debug('执行定时风险检查');

    try {
      const activeConfigs = await this.configRepository.find({
        where: { isActive: true },
      });

      for (const config of activeConfigs) {
        try {
          await this.checkPositionLimits(config.symbol, '');
        } catch (error) {
          this.logger.error(`定时风险检查失败 ${config.symbol}: ${error.message}`);
        }
      }
    } catch (error) {
      this.logger.error(`定时风险检查失败: ${error.message}`);
    }
  }

  /**
   * 计算风险级别
   */
  private calculateRiskLevel(
    positionRatio: number,
    consecutiveFailures: number,
    config: GridTradingConfig,
  ): 'low' | 'medium' | 'high' | 'critical' {
    if (consecutiveFailures >= config.maxRetries) {
      return 'critical';
    }
    
    if (positionRatio >= config.maxPositionRatio * 0.9) {
      return 'high';
    }
    
    if (positionRatio >= config.maxPositionRatio * 0.7 || consecutiveFailures >= 3) {
      return 'medium';
    }
    
    return 'low';
  }

  /**
   * 确定风险状态
   */
  private determineRiskState(metrics: RiskMetrics, config: GridTradingConfig): RiskState {
    if (metrics.riskLevel === 'critical') {
      return RiskState.BLOCK_ALL;
    }
    
    if (metrics.currentPositionRatio >= config.maxPositionRatio) {
      return RiskState.ALLOW_SELL_ONLY;
    }
    
    if (metrics.currentPositionRatio <= config.minPositionRatio) {
      return RiskState.ALLOW_BUY_ONLY;
    }
    
    return RiskState.ALLOW_ALL;
  }

  /**
   * 生成警告信息
   */
  private generateWarnings(metrics: RiskMetrics, config: GridTradingConfig): string[] {
    const warnings: string[] = [];
    
    if (metrics.currentPositionRatio > config.maxPositionRatio * 0.8) {
      warnings.push(`仓位比例接近上限: ${(metrics.currentPositionRatio * 100).toFixed(2)}%`);
    }
    
    if (metrics.consecutiveFailures >= 3) {
      warnings.push(`连续失败次数过多: ${metrics.consecutiveFailures}`);
    }
    
    return warnings;
  }

  /**
   * 生成建议操作
   */
  private generateActions(state: RiskState, warnings: string[]): string[] {
    const actions: string[] = [];
    
    switch (state) {
      case RiskState.ALLOW_SELL_ONLY:
        actions.push('仅允许卖出操作');
        break;
      case RiskState.ALLOW_BUY_ONLY:
        actions.push('仅允许买入操作');
        break;
      case RiskState.BLOCK_ALL:
        actions.push('暂停所有交易操作');
        break;
    }
    
    if (warnings.length > 0) {
      actions.push('建议检查交易参数');
    }
    
    return actions;
  }

  /**
   * 计算总资产价值
   */
  private calculateTotalValue(
    balances: any[],
    currentPrice: number,
    symbol: string,
  ): number {
    // 简化计算，实际应该考虑所有资产
    const [baseAsset, quoteAsset] = symbol.split('/');
    
    const baseBalance = balances.find(b => b.asset === baseAsset)?.total || 0;
    const quoteBalance = balances.find(b => b.asset === quoteAsset)?.total || 0;
    
    return baseBalance * currentPrice + quoteBalance;
  }

  /**
   * 计算仓位价值
   */
  private calculatePositionValue(positions: TradingPosition[], currentPrice: number): number {
    return positions.reduce((total, position) => {
      return total + position.remainingQuantity * currentPrice;
    }, 0);
  }

  /**
   * 获取连续失败次数
   */
  private async getConsecutiveFailures(symbol: string, sessionId: string): Promise<number> {
    const recentOrders = await this.orderRepository.find({
      where: { symbol, sessionId },
      order: { createdAt: 'DESC' },
      take: 10,
    });

    let consecutiveFailures = 0;
    for (const order of recentOrders) {
      if (order.status === OrderStatus.REJECTED || order.status === OrderStatus.CANCELLED) {
        consecutiveFailures++;
      } else if (order.status === OrderStatus.FILLED) {
        break;
      }
    }

    return consecutiveFailures;
  }

  /**
   * 记录风险事件
   */
  private async recordRiskEvent(
    symbol: string,
    sessionId: string,
    metrics: RiskMetrics,
    warnings: string[],
    eventType: RiskEventType = RiskEventType.POSITION_LIMIT_EXCEEDED,
  ): Promise<void> {
    try {
      const riskEvent = this.riskEventRepository.create({
        symbol,
        sessionId,
        eventType,
        riskLevel: metrics.riskLevel as RiskLevel,
        title: this.getRiskEventTitle(eventType),
        description: warnings.join('; '),
        triggerValue: metrics.currentPositionRatio,
        thresholdValue: metrics.maxPositionRatio,
        eventData: {
          metrics,
          warnings,
          timestamp: new Date(),
        },
        actions: this.generateActions(
          this.riskStates.get(symbol)?.state || RiskState.ALLOW_ALL,
          warnings,
        ),
      });

      await this.riskEventRepository.save(riskEvent);
    } catch (error) {
      this.logger.error(`记录风险事件失败: ${error.message}`);
    }
  }

  /**
   * 获取风险事件标题
   */
  private getRiskEventTitle(eventType: RiskEventType): string {
    const titles: Record<RiskEventType, string> = {
      [RiskEventType.POSITION_LIMIT_EXCEEDED]: '仓位限制超出',
      [RiskEventType.CONSECUTIVE_FAILURES]: '连续失败过多',
      [RiskEventType.VOLATILITY_SPIKE]: '波动率异常',
      [RiskEventType.BALANCE_INSUFFICIENT]: '余额不足',
      [RiskEventType.API_ERROR]: 'API错误',
      [RiskEventType.NETWORK_ERROR]: '网络错误',
      [RiskEventType.EMERGENCY_STOP]: '紧急停止',
      [RiskEventType.MANUAL_INTERVENTION]: '人工干预',
    };

    return titles[eventType] || '未知风险事件';
  }
}
