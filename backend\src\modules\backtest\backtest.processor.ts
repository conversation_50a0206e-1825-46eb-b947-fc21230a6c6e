import { Process, Processor } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { Job } from 'bull';
import { BacktestService } from './backtest.service';
import { BacktestEngineService } from './backtest-engine.service';
import { BacktestRequest } from '@signal-dash/shared';

@Processor('backtest')
export class BacktestProcessor {
  private readonly logger = new Logger(BacktestProcessor.name);

  constructor(
    private backtestService: BacktestService,
    private backtestEngine: BacktestEngineService
  ) {}

  @Process('run-backtest')
  async handleBacktest(job: Job<{ jobId: string; request: BacktestRequest }>) {
    const { jobId, request } = job.data;
    
    this.logger.log(`开始处理回测任务: ${jobId}`);

    try {
      // 更新状态为运行中
      await this.backtestService.updateBacktestStatus(jobId, 'RUNNING');

      // 执行回测
      const result = await this.backtestEngine.runBacktest(
        request,
        (progress: number) => {
          // 更新进度
          job.progress(progress);
          this.backtestService.updateBacktestProgress(jobId, progress);
        }
      );

      // 更新状态为完成
      await this.backtestService.updateBacktestStatus(jobId, 'COMPLETED', result);
      
      this.logger.log(`回测任务完成: ${jobId}`);
    } catch (error) {
      this.logger.error(`回测任务失败: ${jobId}`, error);
      await this.backtestService.updateBacktestStatus(jobId, 'FAILED', undefined, error.message);
      throw error;
    }
  }
}
