import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';

export enum OrderSide {
  BUY = 'buy',
  SELL = 'sell',
}

export enum OrderType {
  MARKET = 'market',
  LIMIT = 'limit',
}

export enum OrderStatus {
  PENDING = 'pending',
  OPEN = 'open',
  FILLED = 'filled',
  PARTIALLY_FILLED = 'partially_filled',
  CANCELLED = 'cancelled',
  REJECTED = 'rejected',
  EXPIRED = 'expired',
}

@Entity('order_history')
@Index(['symbol', 'createdAt'])
@Index(['symbol', 'status'])
@Index(['exchangeOrderId'], { unique: true })
export class OrderHistory {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 20 })
  symbol: string;

  @Column({ type: 'varchar', length: 100 })
  sessionId: string;

  @Column({ type: 'varchar', length: 100, unique: true })
  exchangeOrderId: string;

  @Column({
    type: 'enum',
    enum: OrderSide,
  })
  side: OrderSide;

  @Column({
    type: 'enum',
    enum: OrderType,
  })
  type: OrderType;

  @Column({
    type: 'enum',
    enum: OrderStatus,
    default: OrderStatus.PENDING,
  })
  status: OrderStatus;

  @Column({ type: 'decimal', precision: 18, scale: 8 })
  price: number;

  @Column({ type: 'decimal', precision: 18, scale: 8 })
  quantity: number;

  @Column({ type: 'decimal', precision: 18, scale: 8, default: 0 })
  filledQuantity: number;

  @Column({ type: 'decimal', precision: 18, scale: 8, default: 0 })
  remainingQuantity: number;

  @Column({ type: 'decimal', precision: 18, scale: 8, default: 0 })
  averagePrice: number;

  @Column({ type: 'decimal', precision: 18, scale: 8, default: 0 })
  totalValue: number;

  @Column({ type: 'decimal', precision: 18, scale: 8, default: 0 })
  fees: number;

  @Column({ type: 'decimal', precision: 18, scale: 8, default: 0 })
  profit: number;

  @Column({ type: 'decimal', precision: 10, scale: 4, nullable: true })
  gridLevel: number;

  @Column({ type: 'varchar', length: 100, nullable: true })
  positionId: string;

  @Column({ type: 'timestamp', nullable: true })
  filledAt: Date;

  @Column({ type: 'timestamp', nullable: true })
  cancelledAt: Date;

  @Column({ type: 'json', nullable: true })
  exchangeData: Record<string, any>;

  @Column({ type: 'text', nullable: true })
  errorMessage: string;

  @Column({ type: 'int', default: 0 })
  retryCount: number;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
