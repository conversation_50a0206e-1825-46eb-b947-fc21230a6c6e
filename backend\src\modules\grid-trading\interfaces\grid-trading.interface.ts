export interface GridTradingStatus {
  symbol: string;
  isActive: boolean;
  sessionId?: string;
  startTime?: Date;
  currentPrice: number;
  gridSize: number;
  gridStatus: GridStatus;
  performance: TradingPerformance;
  lastUpdate: Date;
}

export interface GridStatus {
  upperBand: number;
  lowerBand: number;
  currentLevel: number;
  totalLevels: number;
  activeBuyOrders: number;
  activeSellOrders: number;
  nextBuyPrice: number;
  nextSellPrice: number;
}

export interface TradingPerformance {
  symbol: string;
  totalTrades: number;
  profitableTrades: number;
  totalProfit: number;
  winRate: number;
  averageProfit: number;
  maxDrawdown?: number;
  sharpeRatio?: number;
  totalFees?: number;
  netProfit?: number;
}

export interface VolatilityMetrics {
  traditionalVolatility: number;
  ewmaVolatility: number;
  hybridVolatility: number;
  annualizedVolatility: number;
  volumeWeightedVolatility?: number;
  smoothedVolatility?: number;
  timestamp: Date;
}

export interface RiskMetrics {
  currentPositionRatio: number;
  maxPositionRatio: number;
  consecutiveFailures: number;
  maxConsecutiveFailures: number;
  lastRiskCheck: Date;
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  warnings: string[];
}

export interface OrderExecutionResult {
  success: boolean;
  orderId?: string;
  executedPrice?: number;
  executedQuantity?: number;
  fees?: number;
  error?: string;
  retryCount?: number;
}

export interface GridLevel {
  level: number;
  price: number;
  side: 'buy' | 'sell';
  quantity: number;
  isActive: boolean;
  orderId?: string;
}

export interface TradingSession {
  sessionId: string;
  symbol: string;
  startTime: Date;
  endTime?: Date;
  status: 'starting' | 'running' | 'stopping' | 'stopped' | 'error';
  config: any;
  statistics: TradingPerformance;
  lastActivity: Date;
}

export interface MarketData {
  symbol: string;
  price: number;
  volume: number;
  timestamp: Date;
  bid: number;
  ask: number;
  spread: number;
}

export interface BalanceInfo {
  asset: string;
  free: number;
  used: number;
  total: number;
}

export interface ExchangeInfo {
  symbol: string;
  baseAsset: string;
  quoteAsset: string;
  minQty: number;
  maxQty: number;
  stepSize: number;
  minPrice: number;
  maxPrice: number;
  tickSize: number;
  minNotional: number;
}

export interface GridTradingEvent {
  type: 'order_filled' | 'grid_adjusted' | 'risk_triggered' | 'session_started' | 'session_stopped';
  symbol: string;
  sessionId: string;
  timestamp: Date;
  data: any;
}

export interface PositionSummary {
  symbol: string;
  totalPositions: number;
  openPositions: number;
  closedPositions: number;
  totalValue: number;
  unrealizedPnl: number;
  realizedPnl: number;
  averageEntryPrice: number;
  currentPrice: number;
}

export interface GridAdjustmentParams {
  newGridSize: number;
  reason: string;
  volatility: number;
  priceChange: number;
  timestamp: Date;
}

export interface FundManagementAction {
  action: 'transfer_to_spot' | 'transfer_to_savings';
  asset: string;
  amount: number;
  reason: string;
  timestamp: Date;
  success: boolean;
  error?: string;
}
