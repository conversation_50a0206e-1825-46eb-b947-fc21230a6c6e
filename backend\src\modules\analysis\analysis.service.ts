import { Injectable, Logger } from '@nestjs/common';
import { TradingPair, StrategyType, AnalyzeResponse, SignalType, STRATEGY_PARAMS } from '@signal-dash/shared';
import { MarketDataService } from '../../common/services/market-data.service';
import { StrategyService } from '../../common/services/strategy.service';

@Injectable()
export class AnalysisService {
  private readonly logger = new Logger(AnalysisService.name);

  constructor(
    private marketDataService: MarketDataService,
    private strategyService: StrategyService
  ) {}

  /**
   * 立即分析指定资产和策略
   */
  async analyzeNow(asset: TradingPair, strategyType: StrategyType): Promise<AnalyzeResponse> {
    this.logger.log(`开始分析: ${asset} 使用 ${strategyType} 策略`);

    try {
      // 获取策略对应的时间框架
      const timeFrame = STRATEGY_PARAMS[strategyType].TIMEFRAME;
      
      // 获取足够的K线数据 (需要200根以上用于计算EMA200)
      const klines = await this.marketDataService.getKlines(asset, timeFrame, 300, false);
      
      if (klines.length < 200) {
        throw new Error(`数据不足，需要至少200根K线，当前只有${klines.length}根`);
      }

      // 使用策略分析
      const signal = this.strategyService.analyzeWithStrategy(strategyType, klines, asset);
      
      if (!signal) {
        throw new Error('策略分析失败，无法生成信号');
      }

      // 生成推荐文本和置信度
      const { recommendation, confidence } = this.generateRecommendation(signal);

      return {
        signal,
        recommendation,
        confidence
      };
    } catch (error) {
      this.logger.error(`分析失败: ${asset} ${strategyType}`, error);
      throw error;
    }
  }

  /**
   * 生成推荐文本和置信度
   */
  private generateRecommendation(signal: any): { recommendation: string; confidence: number } {
    const { signalType, score, reasoning } = signal;
    
    let recommendation: string;
    let confidence: number;

    // 根据信号类型生成推荐
    switch (signalType) {
      case SignalType.BUY:
        recommendation = `🟢 **建议买入**\n\n评分: ${score}/100\n\n分析依据:\n${reasoning.map(r => `• ${r}`).join('\n')}`;
        confidence = Math.min(score / 100, 0.95);
        break;
        
      case SignalType.SELL:
        recommendation = `🔴 **建议卖出**\n\n评分: ${score}/100\n\n分析依据:\n${reasoning.map(r => `• ${r}`).join('\n')}`;
        confidence = Math.min(score / 100, 0.95);
        break;
        
      default:
        recommendation = `⚪️ **建议观望**\n\n评分: ${score}/100\n\n当前市场条件不满足入场要求。\n\n分析依据:\n${reasoning.map(r => `• ${r}`).join('\n')}`;
        confidence = 0.6;
    }

    // 添加风险提示
    if (signal.stopLoss) {
      recommendation += `\n\n💡 **风险管理建议:**\n• 止损价: ${signal.stopLoss.toFixed(4)}`;
    }
    
    if (signal.takeProfit) {
      recommendation += `\n• 止盈价: ${signal.takeProfit.toFixed(4)}`;
    }

    return { recommendation, confidence };
  }
}
