import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Cron, CronExpression } from '@nestjs/schedule';
import { OrderHistory, OrderStatus, OrderSide, OrderType } from '../entities/order-history.entity';
import { TradingPosition, PositionSide, PositionStatus } from '../entities/trading-position.entity';
import { ExchangeClientService, OrderParams } from './exchange-client.service';
import { OrderExecutionResult } from '../interfaces/grid-trading.interface';

export interface OrderRequest {
  symbol: string;
  sessionId: string;
  side: 'buy' | 'sell';
  price: number;
  quantity: number;
  gridLevel?: number;
  positionId?: string;
}

export interface OrderTrackingResult {
  orderId: string;
  status: OrderStatus;
  filledQuantity: number;
  averagePrice: number;
  fees: number;
  isCompleted: boolean;
}

@Injectable()
export class OrderManagerService {
  private readonly logger = new Logger(OrderManagerService.name);
  
  // 订单跟踪队列
  private trackingOrders = new Map<string, {
    orderHistory: OrderHistory;
    retryCount: number;
    lastCheck: Date;
  }>();

  constructor(
    @InjectRepository(OrderHistory)
    private readonly orderRepository: Repository<OrderHistory>,
    @InjectRepository(TradingPosition)
    private readonly positionRepository: Repository<TradingPosition>,
    private readonly exchangeClient: ExchangeClientService,
  ) {}

  /**
   * 执行订单
   */
  async executeOrder(request: OrderRequest): Promise<OrderExecutionResult> {
    this.logger.log(`执行订单: ${request.side} ${request.quantity} ${request.symbol} @ ${request.price}`);

    try {
      // 1. 验证订单参数
      await this.validateOrderRequest(request);

      // 2. 计算精确的订单数量
      const adjustedQuantity = await this.exchangeClient.calculateOrderAmount(
        request.symbol,
        request.quantity * request.price,
        request.price,
      );

      // 3. 创建订单记录
      const orderHistory = await this.createOrderRecord(request, adjustedQuantity);

      // 4. 执行交易所订单
      const orderParams: OrderParams = {
        symbol: request.symbol,
        side: request.side,
        type: 'market', // 使用市价单确保成交
        amount: adjustedQuantity,
      };

      const result = await this.exchangeClient.createOrder(orderParams);

      // 5. 更新订单状态
      if (result.success) {
        await this.updateOrderStatus(orderHistory, {
          exchangeOrderId: result.orderId!,
          status: OrderStatus.OPEN,
          filledQuantity: result.executedQuantity || 0,
          averagePrice: result.executedPrice || request.price,
          fees: result.fees || 0,
        });

        // 6. 添加到跟踪队列
        this.trackingOrders.set(result.orderId!, {
          orderHistory,
          retryCount: 0,
          lastCheck: new Date(),
        });

        // 7. 如果是市价单且已成交，立即处理
        if (result.executedQuantity && result.executedQuantity > 0) {
          await this.handleOrderFilled(orderHistory, result);
        }

        return {
          success: true,
          orderId: result.orderId,
          executedPrice: result.executedPrice,
          executedQuantity: result.executedQuantity,
          fees: result.fees,
        };
      } else {
        // 订单失败
        await this.updateOrderStatus(orderHistory, {
          status: OrderStatus.REJECTED,
          errorMessage: result.error,
        });

        return {
          success: false,
          error: result.error,
        };
      }
    } catch (error) {
      this.logger.error(`订单执行失败: ${error.message}`, error.stack);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * 跟踪订单状态
   */
  async trackOrder(orderId: string): Promise<OrderTrackingResult | null> {
    const tracking = this.trackingOrders.get(orderId);
    if (!tracking) {
      return null;
    }

    try {
      const orderStatus = await this.exchangeClient.getOrderStatus(
        orderId,
        tracking.orderHistory.symbol,
      );

      const result: OrderTrackingResult = {
        orderId,
        status: this.mapExchangeStatus(orderStatus.status),
        filledQuantity: orderStatus.filled || 0,
        averagePrice: orderStatus.average || orderStatus.price || 0,
        fees: orderStatus.fee?.cost || 0,
        isCompleted: ['filled', 'cancelled', 'rejected'].includes(orderStatus.status),
      };

      // 更新数据库记录
      await this.updateOrderStatus(tracking.orderHistory, {
        status: result.status,
        filledQuantity: result.filledQuantity,
        averagePrice: result.averagePrice,
        fees: result.fees,
        exchangeData: orderStatus,
      });

      // 如果订单完成，处理后续逻辑
      if (result.isCompleted) {
        if (result.status === OrderStatus.FILLED) {
          await this.handleOrderFilled(tracking.orderHistory, {
            success: true,
            orderId,
            executedPrice: result.averagePrice,
            executedQuantity: result.filledQuantity,
            fees: result.fees,
          });
        }

        // 从跟踪队列中移除
        this.trackingOrders.delete(orderId);
      } else {
        // 更新跟踪信息
        tracking.lastCheck = new Date();
      }

      return result;
    } catch (error) {
      this.logger.error(`跟踪订单失败 ${orderId}: ${error.message}`);
      tracking.retryCount++;
      
      // 如果重试次数过多，标记为错误
      if (tracking.retryCount > 5) {
        await this.updateOrderStatus(tracking.orderHistory, {
          status: OrderStatus.REJECTED,
          errorMessage: `跟踪失败: ${error.message}`,
        });
        this.trackingOrders.delete(orderId);
      }
      
      return null;
    }
  }

  /**
   * 定时检查订单状态
   */
  @Cron(CronExpression.EVERY_30_SECONDS)
  async checkPendingOrders(): Promise<void> {
    if (this.trackingOrders.size === 0) return;

    this.logger.debug(`检查 ${this.trackingOrders.size} 个待跟踪订单`);

    const promises: Promise<void>[] = [];
    
    for (const [orderId, tracking] of this.trackingOrders.entries()) {
      // 避免过于频繁的检查
      const timeSinceLastCheck = Date.now() - tracking.lastCheck.getTime();
      if (timeSinceLastCheck < 10000) { // 10秒间隔
        continue;
      }

      promises.push(
        this.trackOrder(orderId).then(() => {}).catch(error => {
          this.logger.error(`定时检查订单失败 ${orderId}: ${error.message}`);
        }),
      );
    }

    if (promises.length > 0) {
      await Promise.all(promises);
    }
  }

  /**
   * 取消订单
   */
  async cancelOrder(orderId: string): Promise<boolean> {
    const tracking = this.trackingOrders.get(orderId);
    if (!tracking) {
      this.logger.warn(`未找到跟踪中的订单: ${orderId}`);
      return false;
    }

    try {
      const success = await this.exchangeClient.cancelOrder(
        orderId,
        tracking.orderHistory.symbol,
      );

      if (success) {
        await this.updateOrderStatus(tracking.orderHistory, {
          status: OrderStatus.CANCELLED,
          cancelledAt: new Date(),
        });

        this.trackingOrders.delete(orderId);
        this.logger.log(`订单已取消: ${orderId}`);
      }

      return success;
    } catch (error) {
      this.logger.error(`取消订单失败 ${orderId}: ${error.message}`);
      return false;
    }
  }

  /**
   * 获取活跃订单
   */
  async getActiveOrders(symbol?: string): Promise<OrderHistory[]> {
    const query = this.orderRepository.createQueryBuilder('order')
      .where('order.status IN (:...statuses)', {
        statuses: [OrderStatus.PENDING, OrderStatus.OPEN, OrderStatus.PARTIALLY_FILLED],
      });

    if (symbol) {
      query.andWhere('order.symbol = :symbol', { symbol });
    }

    return query.orderBy('order.createdAt', 'DESC').getMany();
  }

  /**
   * 验证订单请求
   */
  private async validateOrderRequest(request: OrderRequest): Promise<void> {
    // 验证交易对
    const isValidSymbol = await this.exchangeClient.validateSymbol(request.symbol);
    if (!isValidSymbol) {
      throw new Error(`无效的交易对: ${request.symbol}`);
    }

    // 验证数量和价格
    if (request.quantity <= 0) {
      throw new Error('订单数量必须大于0');
    }

    if (request.price <= 0) {
      throw new Error('订单价格必须大于0');
    }

    // 获取交易对信息验证最小数量
    const exchangeInfo = await this.exchangeClient.getExchangeInfo(request.symbol);
    if (request.quantity < exchangeInfo.minQty) {
      throw new Error(`订单数量低于最小限制: ${exchangeInfo.minQty}`);
    }

    const notional = request.quantity * request.price;
    if (notional < exchangeInfo.minNotional) {
      throw new Error(`订单金额低于最小限制: ${exchangeInfo.minNotional}`);
    }
  }

  /**
   * 创建订单记录
   */
  private async createOrderRecord(
    request: OrderRequest,
    adjustedQuantity: number,
  ): Promise<OrderHistory> {
    const orderHistory = this.orderRepository.create({
      symbol: request.symbol,
      sessionId: request.sessionId,
      exchangeOrderId: '', // 稍后更新
      side: request.side === 'buy' ? OrderSide.BUY : OrderSide.SELL,
      type: OrderType.MARKET,
      status: OrderStatus.PENDING,
      price: request.price,
      quantity: adjustedQuantity,
      remainingQuantity: adjustedQuantity,
      gridLevel: request.gridLevel,
      positionId: request.positionId,
    });

    return this.orderRepository.save(orderHistory);
  }

  /**
   * 更新订单状态
   */
  private async updateOrderStatus(
    orderHistory: OrderHistory,
    updates: Partial<OrderHistory> & {
      exchangeOrderId?: string;
      status?: OrderStatus;
      filledQuantity?: number;
      averagePrice?: number;
      fees?: number;
      errorMessage?: string;
      exchangeData?: any;
      cancelledAt?: Date;
    },
  ): Promise<void> {
    Object.assign(orderHistory, updates);

    if (updates.filledQuantity !== undefined) {
      orderHistory.remainingQuantity = orderHistory.quantity - updates.filledQuantity;
    }

    if (updates.status === OrderStatus.FILLED) {
      orderHistory.filledAt = new Date();
    }

    await this.orderRepository.save(orderHistory);
  }

  /**
   * 处理订单成交
   */
  private async handleOrderFilled(
    orderHistory: OrderHistory,
    result: OrderExecutionResult,
  ): Promise<void> {
    this.logger.log(`订单成交: ${orderHistory.exchangeOrderId} - ${result.executedQuantity} @ ${result.executedPrice}`);

    try {
      // 创建或更新仓位记录
      if (orderHistory.positionId) {
        await this.updatePosition(orderHistory, result);
      } else {
        await this.createPosition(orderHistory, result);
      }

      // 计算盈亏（如果有对应的反向订单）
      await this.calculateProfit(orderHistory);

    } catch (error) {
      this.logger.error(`处理订单成交失败: ${error.message}`, error.stack);
    }
  }

  /**
   * 创建仓位记录
   */
  private async createPosition(
    orderHistory: OrderHistory,
    result: OrderExecutionResult,
  ): Promise<void> {
    const position = this.positionRepository.create({
      symbol: orderHistory.symbol,
      sessionId: orderHistory.sessionId,
      side: orderHistory.side === OrderSide.BUY ? PositionSide.LONG : PositionSide.SHORT,
      status: PositionStatus.OPEN,
      entryPrice: result.executedPrice!,
      quantity: result.executedQuantity!,
      remainingQuantity: result.executedQuantity!,
      totalValue: result.executedPrice! * result.executedQuantity!,
      fees: result.fees || 0,
      gridLevel: orderHistory.gridLevel || 0,
      entryOrderId: orderHistory.exchangeOrderId,
    });

    await this.positionRepository.save(position);
  }

  /**
   * 更新仓位记录
   */
  private async updatePosition(
    orderHistory: OrderHistory,
    result: OrderExecutionResult,
  ): Promise<void> {
    const position = await this.positionRepository.findOne({
      where: { id: orderHistory.positionId },
    });

    if (position) {
      // 更新仓位信息
      position.exitPrice = result.executedPrice!;
      position.status = PositionStatus.CLOSED;
      position.closedAt = new Date();
      position.exitOrderId = orderHistory.exchangeOrderId;
      position.fees += result.fees || 0;

      // 计算已实现盈亏
      const pnl = (result.executedPrice! - position.entryPrice) * result.executedQuantity!;
      position.realizedPnl = position.side === PositionSide.LONG ? pnl : -pnl;

      await this.positionRepository.save(position);
    }
  }

  /**
   * 计算盈亏
   */
  private async calculateProfit(orderHistory: OrderHistory): Promise<void> {
    // 简化版本：基于网格级别计算预期盈亏
    if (orderHistory.gridLevel && orderHistory.gridLevel !== 0) {
      const gridProfit = orderHistory.totalValue * 0.01; // 假设1%的网格利润
      orderHistory.profit = orderHistory.side === OrderSide.SELL ? gridProfit : -gridProfit;
      await this.orderRepository.save(orderHistory);
    }
  }

  /**
   * 映射交易所订单状态
   */
  private mapExchangeStatus(exchangeStatus: string): OrderStatus {
    const statusMap: Record<string, OrderStatus> = {
      'open': OrderStatus.OPEN,
      'closed': OrderStatus.FILLED,
      'canceled': OrderStatus.CANCELLED,
      'cancelled': OrderStatus.CANCELLED,
      'rejected': OrderStatus.REJECTED,
      'expired': OrderStatus.EXPIRED,
    };

    return statusMap[exchangeStatus.toLowerCase()] || OrderStatus.PENDING;
  }
}
