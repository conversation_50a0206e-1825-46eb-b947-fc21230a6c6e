import { useState, useEffect } from 'react';
import { useMutation, useQuery } from '@tanstack/react-query';
import { TradingPair, StrategyType, BacktestRequest } from '@signal-dash/shared';
import { api } from '../services/api';
import { websocketService } from '../services/websocket';
import { Loader2, TrendingDown, DollarSign, Percent, BarChart3 } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { zhCN } from 'date-fns/locale';

export function Backtester() {
  const [selectedAsset, setSelectedAsset] = useState<TradingPair>('BTC/USDT');
  const [selectedStrategy, setSelectedStrategy] = useState<StrategyType>(StrategyType.TREND_FOLLOWING);
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [initialCapital, setInitialCapital] = useState(10000);
  const [currentJobId, setCurrentJobId] = useState<string | null>(null);
  const [progress, setProgress] = useState(0);

  // 设置默认日期（过去3个月）
  useEffect(() => {
    const end = new Date();
    const start = new Date();
    start.setMonth(start.getMonth() - 3);

    setEndDate(end.toISOString().split('T')[0]);
    setStartDate(start.toISOString().split('T')[0]);
  }, []);

  // 启动回测
  const backtestMutation = useMutation({
    mutationFn: (request: BacktestRequest) => api.backtest.start(request),
    onSuccess: (data) => {
      setCurrentJobId(data.jobId);
      setProgress(0);
    },
    onError: (error) => {
      console.error('启动回测失败:', error);
    },
  });

  // 获取回测状态
  const { data: jobStatus } = useQuery({
    queryKey: ['backtest-status', currentJobId],
    queryFn: () => currentJobId ? api.backtest.getStatus(currentJobId) : null,
    enabled: !!currentJobId,
    refetchInterval: 2000, // 简化为固定间隔
  });

  // 获取回测结果
  const { data: backtestResult } = useQuery({
    queryKey: ['backtest-result', currentJobId],
    queryFn: () => currentJobId ? api.backtest.getResults(currentJobId) : null,
    enabled: !!currentJobId && jobStatus?.status === 'COMPLETED',
  });

  // WebSocket订阅回测进度
  useEffect(() => {
    if (!currentJobId) return;

    const connectWebSocket = async () => {
      try {
        await websocketService.connect();
        websocketService.subscribeToBacktest(
          currentJobId,
          (data) => {
            setProgress(data.progress);
          },
          (job) => {
            console.log('回测完成:', job);
          }
        );
      } catch (error) {
        console.error('WebSocket连接失败:', error);
      }
    };

    connectWebSocket();

    return () => {
      websocketService.unsubscribeFromBacktest(currentJobId);
    };
  }, [currentJobId]);

  const handleStartBacktest = () => {
    if (!startDate || !endDate || initialCapital <= 0) {
      alert('请填写完整的回测参数');
      return;
    }

    backtestMutation.mutate({
      asset: selectedAsset,
      strategy: selectedStrategy,
      startDate,
      endDate,
      initialCapital,
    });
  };

  const getPerformanceColor = (value: number) => {
    if (value > 0) return 'text-green-600';
    if (value < 0) return 'text-red-600';
    return 'text-gray-600';
  };

  const isRunning = jobStatus?.status === 'RUNNING' || jobStatus?.status === 'PENDING';
  const isCompleted = jobStatus?.status === 'COMPLETED';
  const isFailed = jobStatus?.status === 'FAILED';

  return (
    <div>
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">策略回测</h1>
        <p className="mt-2 text-gray-600">
          在历史数据上验证策略表现
        </p>
      </div>

      <div className="bg-white rounded-lg shadow p-6">
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
          {/* 配置区域 */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">回测配置</h3>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  交易对
                </label>
                <select
                  value={selectedAsset}
                  onChange={(e) => setSelectedAsset(e.target.value as TradingPair)}
                  disabled={isRunning}
                  className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50"
                >
                  <option value="BTC/USDT">BTC/USDT</option>
                  <option value="ETH/USDT">ETH/USDT</option>
                  <option value="SOL/USDT">SOL/USDT</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  策略模型
                </label>
                <div className="space-y-2">
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="strategy"
                      value={StrategyType.TREND_FOLLOWING}
                      checked={selectedStrategy === StrategyType.TREND_FOLLOWING}
                      onChange={(e) => setSelectedStrategy(e.target.value as StrategyType)}
                      disabled={isRunning}
                      className="mr-2"
                    />
                    趋势跟踪策略
                  </label>
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="strategy"
                      value={StrategyType.MEAN_REVERSION}
                      checked={selectedStrategy === StrategyType.MEAN_REVERSION}
                      onChange={(e) => setSelectedStrategy(e.target.value as StrategyType)}
                      disabled={isRunning}
                      className="mr-2"
                    />
                    震荡区间策略
                  </label>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    开始日期
                  </label>
                  <input
                    type="date"
                    value={startDate}
                    onChange={(e) => setStartDate(e.target.value)}
                    disabled={isRunning}
                    className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    结束日期
                  </label>
                  <input
                    type="date"
                    value={endDate}
                    onChange={(e) => setEndDate(e.target.value)}
                    disabled={isRunning}
                    className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  初始资金 (USDT)
                </label>
                <input
                  type="number"
                  value={initialCapital}
                  onChange={(e) => setInitialCapital(Number(e.target.value))}
                  disabled={isRunning}
                  min={100}
                  className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50"
                />
              </div>

              <button
                onClick={handleStartBacktest}
                disabled={backtestMutation.isPending || isRunning}
                className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
              >
                {backtestMutation.isPending || isRunning ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    {isRunning ? '回测中...' : '启动中...'}
                  </>
                ) : (
                  '开始回测'
                )}
              </button>
            </div>
          </div>

          {/* 结果区域 */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">回测结果</h3>

            {isRunning ? (
              <div className="space-y-4">
                <div className="text-center">
                  <Loader2 className="h-8 w-8 mx-auto mb-4 animate-spin text-blue-600" />
                  <p className="text-gray-600">正在回测中...</p>
                  <p className="text-sm text-gray-500 mt-1">
                    任务ID: {currentJobId}
                  </p>
                </div>

                {/* 进度条 */}
                <div className="bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${progress}%` }}
                  />
                </div>
                <p className="text-center text-sm text-gray-600">
                  进度: {progress}%
                </p>
              </div>
            ) : isFailed ? (
              <div className="text-center text-red-500 py-8">
                <p>回测失败</p>
                <p className="text-sm mt-2">{jobStatus?.error}</p>
              </div>
            ) : isCompleted && backtestResult ? (
              <div className="space-y-4">
                {/* 核心指标 */}
                <div className="grid grid-cols-2 gap-4">
                  <div className="bg-gray-50 p-3 rounded-lg">
                    <div className="flex items-center">
                      <DollarSign className="h-5 w-5 text-gray-600 mr-2" />
                      <span className="text-sm text-gray-600">总收益</span>
                    </div>
                    <p className={`text-lg font-semibold ${getPerformanceColor(backtestResult.performance.totalReturn)}`}>
                      ${backtestResult.performance.totalReturn.toFixed(2)}
                    </p>
                  </div>

                  <div className="bg-gray-50 p-3 rounded-lg">
                    <div className="flex items-center">
                      <Percent className="h-5 w-5 text-gray-600 mr-2" />
                      <span className="text-sm text-gray-600">收益率</span>
                    </div>
                    <p className={`text-lg font-semibold ${getPerformanceColor(backtestResult.performance.totalReturnPercent)}`}>
                      {backtestResult.performance.totalReturnPercent.toFixed(2)}%
                    </p>
                  </div>

                  <div className="bg-gray-50 p-3 rounded-lg">
                    <div className="flex items-center">
                      <TrendingDown className="h-5 w-5 text-red-600 mr-2" />
                      <span className="text-sm text-gray-600">最大回撤</span>
                    </div>
                    <p className="text-lg font-semibold text-red-600">
                      {backtestResult.performance.maxDrawdown.toFixed(2)}%
                    </p>
                  </div>

                  <div className="bg-gray-50 p-3 rounded-lg">
                    <div className="flex items-center">
                      <BarChart3 className="h-5 w-5 text-green-600 mr-2" />
                      <span className="text-sm text-gray-600">胜率</span>
                    </div>
                    <p className="text-lg font-semibold text-green-600">
                      {backtestResult.performance.winRate.toFixed(1)}%
                    </p>
                  </div>
                </div>

                {/* 交易统计 */}
                <div className="bg-blue-50 p-4 rounded-lg">
                  <h5 className="font-medium text-blue-900 mb-2">交易统计</h5>
                  <div className="grid grid-cols-3 gap-4 text-sm">
                    <div>
                      <p className="text-blue-700">总交易数</p>
                      <p className="font-semibold">{backtestResult.performance.totalTrades}</p>
                    </div>
                    <div>
                      <p className="text-green-700">盈利交易</p>
                      <p className="font-semibold text-green-600">{backtestResult.performance.winningTrades}</p>
                    </div>
                    <div>
                      <p className="text-red-700">亏损交易</p>
                      <p className="font-semibold text-red-600">{backtestResult.performance.losingTrades}</p>
                    </div>
                  </div>
                </div>

                {/* 盈亏比 */}
                <div className="bg-green-50 p-4 rounded-lg">
                  <div className="flex items-center justify-between">
                    <span className="font-medium text-green-900">盈亏比</span>
                    <span className="text-green-700 font-semibold">
                      {backtestResult.performance.profitFactor.toFixed(2)}
                    </span>
                  </div>
                </div>

                {/* 完成时间 */}
                <div className="text-center text-sm text-gray-500">
                  完成时间: {backtestResult.completedAt ?
                    formatDistanceToNow(new Date(backtestResult.completedAt), {
                      addSuffix: true,
                      locale: zhCN
                    }) : '未知'}
                </div>
              </div>
            ) : (
              <div className="text-center text-gray-500 py-8">
                请配置参数并开始回测
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
