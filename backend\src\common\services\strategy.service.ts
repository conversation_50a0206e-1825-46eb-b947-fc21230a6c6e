import { Injectable } from '@nestjs/common';
import { Kline, TradingSignal, StrategyType, TradingPair } from '@signal-dash/shared';
import { BaseStrategy, TrendFollowingStrategy, MeanReversionStrategy } from '../strategies';

@Injectable()
export class StrategyService {
  private strategies: Map<StrategyType, BaseStrategy> = new Map();
  
  constructor() {
    this.initializeStrategies();
  }
  
  /**
   * 初始化所有策略
   */
  private initializeStrategies(): void {
    this.strategies.set(StrategyType.TREND_FOLLOWING, new TrendFollowingStrategy());
    this.strategies.set(StrategyType.MEAN_REVERSION, new MeanReversionStrategy());
  }
  
  /**
   * 获取指定策略
   */
  getStrategy(strategyType: StrategyType): BaseStrategy | null {
    return this.strategies.get(strategyType) || null;
  }
  
  /**
   * 使用指定策略分析市场
   */
  analyzeWithStrategy(
    strategyType: StrategyType,
    klines: Kline[],
    asset: TradingPair
  ): TradingSignal | null {
    const strategy = this.getStrategy(strategyType);
    if (!strategy) {
      throw new Error(`未找到策略: ${strategyType}`);
    }
    
    return strategy.generateSignal(klines, asset);
  }
  
  /**
   * 使用所有策略分析市场
   */
  analyzeWithAllStrategies(klines: Kline[], asset: TradingPair): TradingSignal[] {
    const signals: TradingSignal[] = [];
    
    for (const [strategyType, strategy] of this.strategies) {
      try {
        const signal = strategy.generateSignal(klines, asset);
        if (signal) {
          signals.push(signal);
        }
      } catch (error) {
        console.error(`策略 ${strategyType} 分析失败:`, error);
      }
    }
    
    return signals;
  }
  
  /**
   * 获取所有可用策略类型
   */
  getAvailableStrategies(): StrategyType[] {
    return Array.from(this.strategies.keys());
  }
  
  /**
   * 验证策略是否存在
   */
  isValidStrategy(strategyType: string): strategyType is StrategyType {
    return this.strategies.has(strategyType as StrategyType);
  }
}
