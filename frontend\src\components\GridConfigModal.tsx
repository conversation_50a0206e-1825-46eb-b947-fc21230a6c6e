import { useState, useEffect } from 'react';
import { X, Info } from 'lucide-react';
import { GridTradingConfig } from '../types/gridTrading';

interface GridConfigModalProps {
  config: GridTradingConfig | null;
  symbol: string;
  onSave: (config: Partial<GridTradingConfig>) => void;
  onClose: () => void;
  isLoading: boolean;
}

export function GridConfigModal({ config, symbol, onSave, onClose, isLoading }: GridConfigModalProps) {
  const [formData, setFormData] = useState({
    symbol: symbol,
    gridSize: 2.0,
    minGridSize: 1.0,
    maxGridSize: 4.0,
    initialCapital: 1000,
    minTradeAmount: 10,
    maxPositionRatio: 0.8,
    minPositionRatio: 0.1,
    enableVolatilityAdjustment: true,
    volatilityWindow: 52,
    ewmaLambda: 0.94,
    hybridWeight: 0.7,
    adjustmentInterval: 3600,
    enableRiskManagement: true,
    maxRetries: 5,
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    if (config) {
      setFormData({
        symbol: config.symbol,
        gridSize: config.gridSize,
        minGridSize: config.minGridSize,
        maxGridSize: config.maxGridSize,
        initialCapital: config.initialCapital,
        minTradeAmount: config.minTradeAmount,
        maxPositionRatio: config.maxPositionRatio,
        minPositionRatio: config.minPositionRatio,
        enableVolatilityAdjustment: config.enableVolatilityAdjustment,
        volatilityWindow: config.volatilityWindow,
        ewmaLambda: config.ewmaLambda,
        hybridWeight: config.hybridWeight,
        adjustmentInterval: config.adjustmentInterval,
        enableRiskManagement: config.enableRiskManagement,
        maxRetries: config.maxRetries,
      });
    } else {
      setFormData(prev => ({ ...prev, symbol }));
    }
  }, [config, symbol]);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (formData.gridSize < 0.1 || formData.gridSize > 10) {
      newErrors.gridSize = '网格大小必须在0.1%到10%之间';
    }

    if (formData.minGridSize >= formData.maxGridSize) {
      newErrors.minGridSize = '最小网格大小必须小于最大网格大小';
    }

    if (formData.initialCapital < 100) {
      newErrors.initialCapital = '初始资金不能少于100';
    }

    if (formData.minTradeAmount < 1) {
      newErrors.minTradeAmount = '最小交易金额不能少于1';
    }

    if (formData.maxPositionRatio <= formData.minPositionRatio) {
      newErrors.maxPositionRatio = '最大仓位比例必须大于最小仓位比例';
    }

    if (formData.volatilityWindow < 10 || formData.volatilityWindow > 200) {
      newErrors.volatilityWindow = '波动率窗口必须在10到200之间';
    }

    if (formData.ewmaLambda < 0.5 || formData.ewmaLambda > 0.99) {
      newErrors.ewmaLambda = 'EWMA参数必须在0.5到0.99之间';
    }

    if (formData.hybridWeight < 0 || formData.hybridWeight > 1) {
      newErrors.hybridWeight = '混合权重必须在0到1之间';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (validateForm()) {
      onSave(formData);
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex min-h-screen items-center justify-center p-4">
        <div className="fixed inset-0 bg-black bg-opacity-25" onClick={onClose} />
        
        <div className="relative bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
          {/* 标题栏 */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">
              {config ? '编辑网格配置' : '创建网格配置'} - {symbol}
            </h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-500"
            >
              <X className="h-6 w-6" />
            </button>
          </div>

          <form onSubmit={handleSubmit} className="p-6 space-y-6">
            {/* 基础配置 */}
            <div>
              <h4 className="text-md font-medium text-gray-900 mb-4">基础配置</h4>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    网格大小 (%)
                  </label>
                  <input
                    type="number"
                    step="0.1"
                    value={formData.gridSize}
                    onChange={(e) => handleInputChange('gridSize', parseFloat(e.target.value))}
                    className={`w-full border rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      errors.gridSize ? 'border-red-300' : 'border-gray-300'
                    }`}
                  />
                  {errors.gridSize && (
                    <p className="text-red-500 text-xs mt-1">{errors.gridSize}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    初始资金 ($)
                  </label>
                  <input
                    type="number"
                    value={formData.initialCapital}
                    onChange={(e) => handleInputChange('initialCapital', parseFloat(e.target.value))}
                    className={`w-full border rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      errors.initialCapital ? 'border-red-300' : 'border-gray-300'
                    }`}
                  />
                  {errors.initialCapital && (
                    <p className="text-red-500 text-xs mt-1">{errors.initialCapital}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    最小网格大小 (%)
                  </label>
                  <input
                    type="number"
                    step="0.1"
                    value={formData.minGridSize}
                    onChange={(e) => handleInputChange('minGridSize', parseFloat(e.target.value))}
                    className={`w-full border rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      errors.minGridSize ? 'border-red-300' : 'border-gray-300'
                    }`}
                  />
                  {errors.minGridSize && (
                    <p className="text-red-500 text-xs mt-1">{errors.minGridSize}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    最大网格大小 (%)
                  </label>
                  <input
                    type="number"
                    step="0.1"
                    value={formData.maxGridSize}
                    onChange={(e) => handleInputChange('maxGridSize', parseFloat(e.target.value))}
                    className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>
            </div>

            {/* 仓位管理 */}
            <div>
              <h4 className="text-md font-medium text-gray-900 mb-4">仓位管理</h4>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    最大仓位比例
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    min="0"
                    max="1"
                    value={formData.maxPositionRatio}
                    onChange={(e) => handleInputChange('maxPositionRatio', parseFloat(e.target.value))}
                    className={`w-full border rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      errors.maxPositionRatio ? 'border-red-300' : 'border-gray-300'
                    }`}
                  />
                  {errors.maxPositionRatio && (
                    <p className="text-red-500 text-xs mt-1">{errors.maxPositionRatio}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    最小仓位比例
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    min="0"
                    max="1"
                    value={formData.minPositionRatio}
                    onChange={(e) => handleInputChange('minPositionRatio', parseFloat(e.target.value))}
                    className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    最小交易金额 ($)
                  </label>
                  <input
                    type="number"
                    value={formData.minTradeAmount}
                    onChange={(e) => handleInputChange('minTradeAmount', parseFloat(e.target.value))}
                    className={`w-full border rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      errors.minTradeAmount ? 'border-red-300' : 'border-gray-300'
                    }`}
                  />
                  {errors.minTradeAmount && (
                    <p className="text-red-500 text-xs mt-1">{errors.minTradeAmount}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    最大重试次数
                  </label>
                  <input
                    type="number"
                    min="1"
                    max="10"
                    value={formData.maxRetries}
                    onChange={(e) => handleInputChange('maxRetries', parseInt(e.target.value))}
                    className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>
            </div>

            {/* 高级配置 */}
            <div>
              <h4 className="text-md font-medium text-gray-900 mb-4">高级配置</h4>
              
              {/* 开关选项 */}
              <div className="space-y-4 mb-4">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="enableVolatilityAdjustment"
                    checked={formData.enableVolatilityAdjustment}
                    onChange={(e) => handleInputChange('enableVolatilityAdjustment', e.target.checked)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="enableVolatilityAdjustment" className="ml-2 text-sm text-gray-700">
                    启用波动率自适应调整
                  </label>
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="enableRiskManagement"
                    checked={formData.enableRiskManagement}
                    onChange={(e) => handleInputChange('enableRiskManagement', e.target.checked)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="enableRiskManagement" className="ml-2 text-sm text-gray-700">
                    启用风险管理
                  </label>
                </div>
              </div>

              {/* 波动率参数 */}
              {formData.enableVolatilityAdjustment && (
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      波动率窗口
                    </label>
                    <input
                      type="number"
                      min="10"
                      max="200"
                      value={formData.volatilityWindow}
                      onChange={(e) => handleInputChange('volatilityWindow', parseInt(e.target.value))}
                      className={`w-full border rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                        errors.volatilityWindow ? 'border-red-300' : 'border-gray-300'
                      }`}
                    />
                    {errors.volatilityWindow && (
                      <p className="text-red-500 text-xs mt-1">{errors.volatilityWindow}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      EWMA参数
                    </label>
                    <input
                      type="number"
                      step="0.01"
                      min="0.5"
                      max="0.99"
                      value={formData.ewmaLambda}
                      onChange={(e) => handleInputChange('ewmaLambda', parseFloat(e.target.value))}
                      className={`w-full border rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                        errors.ewmaLambda ? 'border-red-300' : 'border-gray-300'
                      }`}
                    />
                    {errors.ewmaLambda && (
                      <p className="text-red-500 text-xs mt-1">{errors.ewmaLambda}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      混合权重
                    </label>
                    <input
                      type="number"
                      step="0.01"
                      min="0"
                      max="1"
                      value={formData.hybridWeight}
                      onChange={(e) => handleInputChange('hybridWeight', parseFloat(e.target.value))}
                      className={`w-full border rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                        errors.hybridWeight ? 'border-red-300' : 'border-gray-300'
                      }`}
                    />
                    {errors.hybridWeight && (
                      <p className="text-red-500 text-xs mt-1">{errors.hybridWeight}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      调整间隔 (秒)
                    </label>
                    <input
                      type="number"
                      min="300"
                      max="86400"
                      value={formData.adjustmentInterval}
                      onChange={(e) => handleInputChange('adjustmentInterval', parseInt(e.target.value))}
                      className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                </div>
              )}
            </div>

            {/* 提示信息 */}
            <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
              <div className="flex">
                <Info className="h-5 w-5 text-blue-400 mt-0.5" />
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-blue-800">配置说明</h3>
                  <div className="mt-2 text-sm text-blue-700">
                    <ul className="list-disc list-inside space-y-1">
                      <li>网格大小决定买卖点的间距，建议根据资产波动率调整</li>
                      <li>启用波动率调整可以根据市场情况自动优化网格大小</li>
                      <li>风险管理功能可以在异常情况下自动停止交易</li>
                      <li>建议先进行小额测试，确认策略效果后再增加资金</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>

            {/* 按钮 */}
            <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                取消
              </button>
              <button
                type="submit"
                disabled={isLoading}
                className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
              >
                {isLoading ? '保存中...' : '保存配置'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
