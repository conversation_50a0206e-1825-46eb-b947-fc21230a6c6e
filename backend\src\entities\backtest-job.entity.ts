import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { BacktestRequest, BacktestResult } from '@signal-dash/shared';

@Entity('backtest_jobs')
export class BacktestJobEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({
    type: 'text',
    default: 'PENDING',
  })
  status: 'PENDING' | 'RUNNING' | 'COMPLETED' | 'FAILED';

  @Column('integer', { default: 0 })
  progress: number;

  @Column({
    type: 'text',
    transformer: {
      to: (value: BacktestRequest) => JSON.stringify(value),
      from: (value: string) => JSON.parse(value),
    },
  })
  request: BacktestRequest;

  @Column({
    type: 'text',
    nullable: true,
    transformer: {
      to: (value: BacktestResult | null) => value ? JSON.stringify(value) : null,
      from: (value: string | null) => value ? JSON.parse(value) : null,
    },
  })
  result?: BacktestResult;

  @Column('text', { nullable: true })
  error?: string;

  @Column('integer')
  createdAt: number;

  @Column('integer')
  updatedAt: number;

  @CreateDateColumn()
  dbCreatedAt: Date;

  @UpdateDateColumn()
  dbUpdatedAt: Date;
}
