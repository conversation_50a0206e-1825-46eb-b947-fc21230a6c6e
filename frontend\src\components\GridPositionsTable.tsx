import { useState } from 'react';
import { TrendingUp, TrendingDown, X, Eye } from 'lucide-react';
import { TradingPosition } from '../types/gridTrading';

interface GridPositionsTableProps {
  positions: {
    positions: TradingPosition[];
    total: number;
    summary: {
      symbol: string;
      totalPositions: number;
      openPositions: number;
      closedPositions: number;
      totalValue: number;
      unrealizedPnl: number;
      realizedPnl: number;
      averageEntryPrice: number;
      currentPrice: number;
    };
  };
}

export function GridPositionsTable({ positions }: GridPositionsTableProps) {
  const [filter, setFilter] = useState<'all' | 'open' | 'closed'>('all');
  const [selectedPosition, setSelectedPosition] = useState<TradingPosition | null>(null);

  const filteredPositions = positions.positions.filter(position => {
    if (filter === 'all') return true;
    if (filter === 'open') return position.status === 'OPEN';
    if (filter === 'closed') return position.status === 'CLOSED';
    return true;
  });

  const formatCurrency = (value: number) => {
    return value.toLocaleString(undefined, { 
      style: 'currency', 
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 4
    });
  };

  const formatPercentage = (value: number) => {
    return `${value >= 0 ? '+' : ''}${value.toFixed(2)}%`;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'OPEN': return 'bg-green-100 text-green-800';
      case 'CLOSED': return 'bg-gray-100 text-gray-800';
      case 'CANCELLED': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getSideColor = (side: string) => {
    return side === 'LONG' ? 'text-green-600' : 'text-red-600';
  };

  const calculatePnlPercentage = (position: TradingPosition) => {
    if (position.status === 'CLOSED' && position.exitPrice) {
      const pnlPercent = position.side === 'LONG' 
        ? ((position.exitPrice - position.entryPrice) / position.entryPrice) * 100
        : ((position.entryPrice - position.exitPrice) / position.entryPrice) * 100;
      return pnlPercent;
    } else if (position.status === 'OPEN') {
      // 需要当前价格来计算未实现盈亏百分比
      const currentPrice = positions.summary.currentPrice;
      const pnlPercent = position.side === 'LONG'
        ? ((currentPrice - position.entryPrice) / position.entryPrice) * 100
        : ((position.entryPrice - currentPrice) / position.entryPrice) * 100;
      return pnlPercent;
    }
    return 0;
  };

  return (
    <div className="space-y-4">
      {/* 汇总信息 */}
      <div className="px-6 py-4 bg-gray-50 border-b border-gray-200">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div>
            <span className="text-gray-600">总仓位:</span>
            <span className="ml-2 font-medium">{positions.summary.totalPositions}</span>
          </div>
          <div>
            <span className="text-gray-600">开仓:</span>
            <span className="ml-2 font-medium text-green-600">{positions.summary.openPositions}</span>
          </div>
          <div>
            <span className="text-gray-600">平均成本:</span>
            <span className="ml-2 font-medium">{formatCurrency(positions.summary.averageEntryPrice)}</span>
          </div>
          <div>
            <span className="text-gray-600">当前价格:</span>
            <span className="ml-2 font-medium">{formatCurrency(positions.summary.currentPrice)}</span>
          </div>
        </div>
      </div>

      {/* 过滤器 */}
      <div className="px-6 flex items-center space-x-4">
        <span className="text-sm font-medium text-gray-700">筛选:</span>
        <div className="flex space-x-2">
          {[
            { key: 'all', label: '全部' },
            { key: 'open', label: '开仓' },
            { key: 'closed', label: '已平仓' },
          ].map(({ key, label }) => (
            <button
              key={key}
              onClick={() => setFilter(key as any)}
              className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                filter === key
                  ? 'bg-blue-100 text-blue-700'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
              }`}
            >
              {label}
            </button>
          ))}
        </div>
      </div>

      {/* 表格 */}
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                仓位信息
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                价格
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                数量
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                价值
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                盈亏
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                时间
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                操作
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {filteredPositions.map((position) => {
              const pnlPercent = calculatePnlPercentage(position);
              const isProfit = position.status === 'CLOSED' 
                ? position.realizedPnl >= 0 
                : pnlPercent >= 0;

              return (
                <tr key={position.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div>
                        <div className="flex items-center space-x-2">
                          <span className={`font-medium ${getSideColor(position.side)}`}>
                            {position.side}
                          </span>
                          <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(position.status)}`}>
                            {position.status}
                          </span>
                        </div>
                        <div className="text-sm text-gray-500">
                          网格级别: {position.gridLevel}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm">
                      <div className="font-medium text-gray-900">
                        入场: {formatCurrency(position.entryPrice)}
                      </div>
                      {position.exitPrice && (
                        <div className="text-gray-500">
                          出场: {formatCurrency(position.exitPrice)}
                        </div>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm">
                      <div className="font-medium text-gray-900">
                        {position.quantity.toFixed(6)}
                      </div>
                      {position.remainingQuantity !== position.quantity && (
                        <div className="text-gray-500">
                          剩余: {position.remainingQuantity.toFixed(6)}
                        </div>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">
                      {formatCurrency(position.totalValue)}
                    </div>
                    <div className="text-xs text-gray-500">
                      手续费: {formatCurrency(position.fees)}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      {isProfit ? (
                        <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
                      ) : (
                        <TrendingDown className="h-4 w-4 text-red-500 mr-1" />
                      )}
                      <div>
                        <div className={`text-sm font-medium ${isProfit ? 'text-green-600' : 'text-red-600'}`}>
                          {position.status === 'CLOSED' 
                            ? formatCurrency(position.realizedPnl)
                            : formatCurrency(pnlPercent * position.totalValue / 100)
                          }
                        </div>
                        <div className={`text-xs ${isProfit ? 'text-green-500' : 'text-red-500'}`}>
                          {formatPercentage(pnlPercent)}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <div>
                      开仓: {new Date(position.createdAt).toLocaleDateString()}
                    </div>
                    {position.closedAt && (
                      <div>
                        平仓: {new Date(position.closedAt).toLocaleDateString()}
                      </div>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <button
                      onClick={() => setSelectedPosition(position)}
                      className="text-blue-600 hover:text-blue-900 mr-3"
                    >
                      <Eye className="h-4 w-4" />
                    </button>
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>

        {filteredPositions.length === 0 && (
          <div className="text-center py-8">
            <p className="text-gray-500">暂无仓位数据</p>
          </div>
        )}
      </div>

      {/* 仓位详情模态框 */}
      {selectedPosition && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex min-h-screen items-center justify-center p-4">
            <div className="fixed inset-0 bg-black bg-opacity-25" onClick={() => setSelectedPosition(null)} />
            
            <div className="relative bg-white rounded-lg shadow-xl max-w-2xl w-full">
              <div className="flex items-center justify-between p-6 border-b border-gray-200">
                <h3 className="text-lg font-medium text-gray-900">仓位详情</h3>
                <button
                  onClick={() => setSelectedPosition(null)}
                  className="text-gray-400 hover:text-gray-500"
                >
                  <X className="h-6 w-6" />
                </button>
              </div>

              <div className="p-6 space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-gray-500">仓位ID</label>
                    <p className="text-sm text-gray-900 font-mono">{selectedPosition.id}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">交易对</label>
                    <p className="text-sm text-gray-900">{selectedPosition.symbol}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">方向</label>
                    <p className={`text-sm font-medium ${getSideColor(selectedPosition.side)}`}>
                      {selectedPosition.side}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">状态</label>
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(selectedPosition.status)}`}>
                      {selectedPosition.status}
                    </span>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">入场价格</label>
                    <p className="text-sm text-gray-900">{formatCurrency(selectedPosition.entryPrice)}</p>
                  </div>
                  {selectedPosition.exitPrice && (
                    <div>
                      <label className="text-sm font-medium text-gray-500">出场价格</label>
                      <p className="text-sm text-gray-900">{formatCurrency(selectedPosition.exitPrice)}</p>
                    </div>
                  )}
                  <div>
                    <label className="text-sm font-medium text-gray-500">数量</label>
                    <p className="text-sm text-gray-900">{selectedPosition.quantity.toFixed(6)}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">剩余数量</label>
                    <p className="text-sm text-gray-900">{selectedPosition.remainingQuantity.toFixed(6)}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">总价值</label>
                    <p className="text-sm text-gray-900">{formatCurrency(selectedPosition.totalValue)}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">已实现盈亏</label>
                    <p className={`text-sm font-medium ${selectedPosition.realizedPnl >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                      {formatCurrency(selectedPosition.realizedPnl)}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">手续费</label>
                    <p className="text-sm text-gray-900">{formatCurrency(selectedPosition.fees)}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">网格级别</label>
                    <p className="text-sm text-gray-900">{selectedPosition.gridLevel}</p>
                  </div>
                </div>

                {selectedPosition.entryOrderId && (
                  <div>
                    <label className="text-sm font-medium text-gray-500">入场订单ID</label>
                    <p className="text-sm text-gray-900 font-mono">{selectedPosition.entryOrderId}</p>
                  </div>
                )}

                {selectedPosition.exitOrderId && (
                  <div>
                    <label className="text-sm font-medium text-gray-500">出场订单ID</label>
                    <p className="text-sm text-gray-900 font-mono">{selectedPosition.exitOrderId}</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
