import { BaseIndicator } from './base.indicator';

/**
 * 相对强弱指数 (RSI)
 */
export class RSIIndicator extends BaseIndicator {
  constructor(period: number = 14) {
    super(period);
  }
  
  /**
   * 计算RSI值
   */
  calculate(): number[] {
    const closes = this.getClosePrices();
    if (closes.length < this.period + 1) {
      return [];
    }
    
    const result: number[] = [];
    const gains: number[] = [];
    const losses: number[] = [];
    
    // 计算价格变化
    for (let i = 1; i < closes.length; i++) {
      const change = closes[i] - closes[i - 1];
      gains.push(change > 0 ? change : 0);
      losses.push(change < 0 ? Math.abs(change) : 0);
    }
    
    if (gains.length < this.period) {
      return [];
    }
    
    // 计算第一个RSI值
    let avgGain = gains.slice(0, this.period).reduce((a, b) => a + b, 0) / this.period;
    let avgLoss = losses.slice(0, this.period).reduce((a, b) => a + b, 0) / this.period;
    
    let rs = avgGain / (avgLoss || 0.0001); // 避免除零
    let rsi = 100 - (100 / (1 + rs));
    result.push(rsi);
    
    // 计算后续RSI值 (使用Wilder's smoothing)
    for (let i = this.period; i < gains.length; i++) {
      avgGain = ((avgGain * (this.period - 1)) + gains[i]) / this.period;
      avgLoss = ((avgLoss * (this.period - 1)) + losses[i]) / this.period;
      
      rs = avgGain / (avgLoss || 0.0001);
      rsi = 100 - (100 / (1 + rs));
      result.push(rsi);
    }
    
    return result;
  }
  
  /**
   * 获取最新RSI值
   */
  getLatest(): number | null {
    const rsiValues = this.calculate();
    return rsiValues.length > 0 ? rsiValues[rsiValues.length - 1] : null;
  }
  
  /**
   * 判断是否超买
   */
  isOverbought(threshold: number = 70): boolean {
    const latest = this.getLatest();
    return latest !== null && latest > threshold;
  }
  
  /**
   * 判断是否超卖
   */
  isOversold(threshold: number = 30): boolean {
    const latest = this.getLatest();
    return latest !== null && latest < threshold;
  }
}
