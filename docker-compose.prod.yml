version: '3.8'

services:
  # Redis服务
  redis:
    image: redis:7-alpine
    container_name: signal-dash-redis-prod
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3

  # 后端服务
  backend:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: signal-dash-backend-prod
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - DATABASE_PATH=/app/data/signal-dash.db
      - BINANCE_API_URL=https://api.binance.com
      - BINANCE_TESTNET=false
      - NTFY_URL=https://ntfy.sh
      - NTFY_TOPIC=signal-dash-pro
      - NTFY_ENABLED=false
    volumes:
      - app_data:/app/data
      - ./logs:/app/logs
    depends_on:
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: signal-dash-nginx-prod
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - backend
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  redis_data:
    driver: local
  app_data:
    driver: local

networks:
  default:
    name: signal-dash-network
