import { Controller, Get, Put, Body, Post, HttpException, HttpStatus } from '@nestjs/common';
import { ConfigurationService } from './configuration.service';
import { SystemConfig } from '@signal-dash/shared';
import { UpdateConfigDto } from './dto/update-config.dto';

@Controller('config')
export class ConfigurationController {
  constructor(private readonly configService: ConfigurationService) {}

  @Get()
  async getConfig(): Promise<SystemConfig> {
    try {
      return await this.configService.getSystemConfig();
    } catch (error) {
      throw new HttpException(
        `获取配置失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Put()
  async updateConfig(@Body() updateDto: UpdateConfigDto): Promise<{ success: boolean }> {
    try {
      await this.configService.updateSystemConfig(updateDto);
      return { success: true };
    } catch (error) {
      throw new HttpException(
        `更新配置失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Post('test-notification')
  async testNotification(): Promise<{ success: boolean; message: string }> {
    try {
      const success = await this.configService.testNotification();
      return {
        success,
        message: success ? '通知测试成功' : '通知测试失败，请检查配置'
      };
    } catch (error) {
      throw new HttpException(
        `测试通知失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('system-status')
  async getSystemStatus(): Promise<{
    database: boolean;
    binanceApi: boolean;
    redis: boolean;
    notification: boolean;
  }> {
    try {
      return await this.configService.getSystemStatus();
    } catch (error) {
      throw new HttpException(
        `获取系统状态失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }
}
