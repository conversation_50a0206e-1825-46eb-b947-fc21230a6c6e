import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  MessageBody,
  ConnectedSocket,
  OnGatewayConnection,
  OnGatewayDisconnect,
} from '@nestjs/websockets';
import { Logger } from '@nestjs/common';
import { Server, Socket } from 'socket.io';
import { TradingSignal, BacktestJob, WS_EVENTS } from '@signal-dash/shared';

@WebSocketGateway({
  cors: {
    origin: process.env.FRONTEND_URL || 'http://localhost:3001',
    credentials: true,
  },
})
export class WebsocketGateway implements OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer()
  server: Server;

  private readonly logger = new Logger(WebsocketGateway.name);
  private connectedClients = new Map<string, Socket>();

  handleConnection(client: Socket) {
    this.connectedClients.set(client.id, client);
    this.logger.log(`客户端连接: ${client.id}`);
  }

  handleDisconnect(client: Socket) {
    this.connectedClients.delete(client.id);
    this.logger.log(`客户端断开: ${client.id}`);
  }

  /**
   * 订阅信号更新
   */
  @SubscribeMessage('subscribe:signals')
  handleSubscribeSignals(@ConnectedSocket() client: Socket) {
    client.join('signals');
    this.logger.log(`客户端 ${client.id} 订阅信号更新`);
    return { event: 'subscribed', data: 'signals' };
  }

  /**
   * 取消订阅信号更新
   */
  @SubscribeMessage('unsubscribe:signals')
  handleUnsubscribeSignals(@ConnectedSocket() client: Socket) {
    client.leave('signals');
    this.logger.log(`客户端 ${client.id} 取消订阅信号更新`);
    return { event: 'unsubscribed', data: 'signals' };
  }

  /**
   * 订阅回测进度
   */
  @SubscribeMessage('subscribe:backtest')
  handleSubscribeBacktest(
    @MessageBody() data: { jobId: string },
    @ConnectedSocket() client: Socket
  ) {
    const room = `backtest:${data.jobId}`;
    client.join(room);
    this.logger.log(`客户端 ${client.id} 订阅回测进度: ${data.jobId}`);
    return { event: 'subscribed', data: room };
  }

  /**
   * 取消订阅回测进度
   */
  @SubscribeMessage('unsubscribe:backtest')
  handleUnsubscribeBacktest(
    @MessageBody() data: { jobId: string },
    @ConnectedSocket() client: Socket
  ) {
    const room = `backtest:${data.jobId}`;
    client.leave(room);
    this.logger.log(`客户端 ${client.id} 取消订阅回测进度: ${data.jobId}`);
    return { event: 'unsubscribed', data: room };
  }

  /**
   * 订阅市场数据更新
   */
  @SubscribeMessage('subscribe:market')
  handleSubscribeMarket(
    @MessageBody() data: { assets: string[] },
    @ConnectedSocket() client: Socket
  ) {
    for (const asset of data.assets) {
      client.join(`market:${asset}`);
    }
    this.logger.log(`客户端 ${client.id} 订阅市场数据: ${data.assets.join(', ')}`);
    return { event: 'subscribed', data: data.assets };
  }

  /**
   * 广播新信号
   */
  broadcastSignal(signal: TradingSignal) {
    this.server.to('signals').emit(WS_EVENTS.SIGNAL_CREATED, signal);
    this.logger.log(`广播新信号: ${signal.asset} ${signal.signalType}`);
  }

  /**
   * 广播回测进度
   */
  broadcastBacktestProgress(jobId: string, progress: number) {
    const room = `backtest:${jobId}`;
    this.server.to(room).emit(WS_EVENTS.BACKTEST_PROGRESS, { jobId, progress });
    this.logger.debug(`广播回测进度: ${jobId} ${progress}%`);
  }

  /**
   * 广播回测完成
   */
  broadcastBacktestCompleted(job: BacktestJob) {
    const room = `backtest:${job.id}`;
    this.server.to(room).emit(WS_EVENTS.BACKTEST_COMPLETED, job);
    this.logger.log(`广播回测完成: ${job.id}`);
  }

  /**
   * 广播市场数据更新
   */
  broadcastMarketDataUpdate(asset: string, data: any) {
    const room = `market:${asset}`;
    this.server.to(room).emit(WS_EVENTS.MARKET_DATA_UPDATE, { asset, data });
    this.logger.debug(`广播市场数据更新: ${asset}`);
  }

  /**
   * 获取连接的客户端数量
   */
  getConnectedClientsCount(): number {
    return this.connectedClients.size;
  }

  /**
   * 向所有客户端广播消息
   */
  broadcast(event: string, data: any) {
    this.server.emit(event, data);
  }

  /**
   * 向特定房间广播消息
   */
  broadcastToRoom(room: string, event: string, data: any) {
    this.server.to(room).emit(event, data);
  }
}
