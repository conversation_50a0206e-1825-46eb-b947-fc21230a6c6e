# Signal-Dash Pro

本地化交易决策平台，支持自动信号监控、手动即时分析和策略历史回测。

## 功能特性

- 🔄 **自动信号模式**: 7x24小时监控市场，通过ntfy推送交易机会
- 🔍 **手动分析模式**: 即时分析当前市场状况，提供交易建议
- 📊 **策略回测模式**: 在历史数据上验证策略表现
- 💰 **支持币种**: BTC、ETH、SOL
- 🎯 **双策略系统**: 趋势跟踪 + 震荡区间策略

## 技术架构

- **后端**: NestJS + TypeScript + SQLite + BullMQ
- **前端**: React + TypeScript + Tailwind CSS + Vite
- **部署**: Docker + Docker Compose

## 快速开始

### 使用Docker (推荐)

1. 克隆项目并进入目录
2. 复制环境配置文件：`cp .env.example .env`
3. 启动服务：`docker-compose up -d`
4. 访问前端：http://localhost:3001
5. 访问后端API：http://localhost:3000

### 本地开发

1. 安装依赖：`npm install`
2. 构建共享模块：`npm run build:shared`
3. 启动开发服务：`npm run dev`

## 项目结构

```
signal-dash-pro/
├── backend/          # NestJS后端服务
├── frontend/         # React前端应用
├── shared/           # 共享类型和工具
├── data/             # 数据存储目录
├── config/           # 配置文件目录
└── docker-compose.yml
```

## 配置说明

主要配置项在 `.env` 文件中：

- `NTFY_URL`: ntfy推送服务地址
- `NTFY_TOPIC`: 推送主题名称
- `MONITORING_INTERVAL`: 监控间隔(分钟)
- `DATABASE_PATH`: SQLite数据库路径

## API文档

- `GET /api/health` - 健康检查
- `POST /api/analyze-now` - 手动分析
- `POST /api/backtest` - 启动回测
- `GET /api/backtest/:jobId/status` - 回测进度
- `GET /api/backtest/:jobId/results` - 回测结果
