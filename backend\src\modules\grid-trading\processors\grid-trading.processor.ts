import { Processor, Process } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { Job } from 'bull';
import { GridTradingConfig } from '../entities/grid-trading-config.entity';
import { StartGridTradingDto } from '../dto/start-grid-trading.dto';
import { GridStrategyService } from '../strategies/grid-strategy.service';
import { VolatilityCalculatorService } from '../strategies/volatility-calculator.service';
import { ExchangeClientService } from '../exchange/exchange-client.service';
import { OrderManagerService } from '../exchange/order-manager.service';
import { RiskManagerService, RiskState } from '../risk-management/risk-manager.service';
import { PositionControllerService } from '../risk-management/position-controller.service';

export interface GridTradingJobData {
  sessionId: string;
  config: GridTradingConfig;
  startDto: StartGridTradingDto;
}

@Processor('grid-trading')
export class GridTradingProcessor {
  private readonly logger = new Logger(GridTradingProcessor.name);
  
  // 活跃的交易会话
  private activeSessions = new Map<string, {
    isRunning: boolean;
    lastActivity: Date;
    consecutiveErrors: number;
  }>();

  constructor(
    private readonly gridStrategy: GridStrategyService,
    private readonly volatilityCalculator: VolatilityCalculatorService,
    private readonly exchangeClient: ExchangeClientService,
    private readonly orderManager: OrderManagerService,
    private readonly riskManager: RiskManagerService,
    private readonly positionController: PositionControllerService,
  ) {}

  @Process('start-grid-trading')
  async handleGridTrading(job: Job<GridTradingJobData>) {
    const { sessionId, config, startDto } = job.data;
    
    this.logger.log(`开始网格交易任务: ${sessionId} - ${config.symbol}`);

    // 初始化会话状态
    this.activeSessions.set(sessionId, {
      isRunning: true,
      lastActivity: new Date(),
      consecutiveErrors: 0,
    });

    try {
      // 加载市场数据
      await this.exchangeClient.loadMarkets();
      
      // 验证交易对
      const isValidSymbol = await this.exchangeClient.validateSymbol(config.symbol);
      if (!isValidSymbol) {
        throw new Error(`无效的交易对: ${config.symbol}`);
      }

      // 开始主循环
      await this.runTradingLoop(sessionId, config, job);

    } catch (error) {
      this.logger.error(`网格交易任务失败 ${sessionId}: ${error.message}`, error.stack);
      throw error;
    } finally {
      // 清理会话状态
      this.activeSessions.delete(sessionId);
      this.logger.log(`网格交易任务结束: ${sessionId}`);
    }
  }

  /**
   * 主交易循环
   */
  private async runTradingLoop(
    sessionId: string,
    config: GridTradingConfig,
    job: Job<GridTradingJobData>,
  ): Promise<void> {
    const session = this.activeSessions.get(sessionId);
    if (!session) return;

    const maxConsecutiveErrors = 5;
    const loopInterval = 5000; // 5秒循环间隔

    while (session.isRunning && session.consecutiveErrors < maxConsecutiveErrors) {
      try {
        // 检查任务是否被取消
        if (job.isFailed() || job.isCompleted()) {
          this.logger.log(`任务已停止: ${sessionId}`);
          break;
        }

        // 更新进度
        await job.progress(50);

        // 执行一轮交易逻辑
        await this.executeTradingCycle(sessionId, config);

        // 更新活动时间
        session.lastActivity = new Date();
        session.consecutiveErrors = 0;

        // 等待下一轮
        await this.sleep(loopInterval);

      } catch (error) {
        session.consecutiveErrors++;
        this.logger.error(
          `交易循环错误 ${sessionId} (第${session.consecutiveErrors}次): ${error.message}`,
          error.stack,
        );

        // 记录错误到风险管理器
        await this.riskManager.recordTradingFailure(
          config.symbol,
          sessionId,
          error.message,
        );

        // 错误后等待更长时间
        await this.sleep(loopInterval * 2);
      }
    }

    if (session.consecutiveErrors >= maxConsecutiveErrors) {
      this.logger.error(`连续错误过多，停止交易会话: ${sessionId}`);
      throw new Error(`连续错误过多: ${session.consecutiveErrors}`);
    }
  }

  /**
   * 执行一轮交易周期
   */
  private async executeTradingCycle(
    sessionId: string,
    config: GridTradingConfig,
  ): Promise<void> {
    const symbol = config.symbol;

    try {
      // 1. 获取市场数据
      const klines = await this.exchangeClient.getKlineData(
        symbol,
        '4h',
        config.volatilityWindow + 10,
      );

      if (klines.length < 10) {
        this.logger.warn(`K线数据不足: ${symbol} - ${klines.length}`);
        return;
      }

      // 2. 风险检查
      const riskCheck = await this.riskManager.checkPositionLimits(symbol, sessionId);
      
      if (riskCheck.state === RiskState.BLOCK_ALL) {
        this.logger.warn(`风险控制阻止交易: ${symbol} - ${riskCheck.warnings.join(', ')}`);
        return;
      }

      // 3. 波动率检查
      if (config.enableVolatilityAdjustment) {
        const volatilityMetrics = await this.volatilityCalculator.calculateVolatilityMetrics(
          symbol,
          klines,
          {
            window: config.volatilityWindow,
            ewmaLambda: config.ewmaLambda,
            hybridWeight: config.hybridWeight,
          },
        );

        // 检查波动率风险
        const hasVolatilityRisk = await this.riskManager.checkVolatilityRisk(
          symbol,
          volatilityMetrics.annualizedVolatility,
          config,
        );

        if (hasVolatilityRisk) {
          this.logger.warn(`波动率风险，暂停交易: ${symbol}`);
          return;
        }
      }

      // 4. 网格策略分析
      const gridSignal = await this.gridStrategy.analyzeMarket(symbol, klines, config);

      // 5. 执行交易信号
      if (gridSignal.type !== 'hold') {
        await this.executeGridSignal(sessionId, config, gridSignal, riskCheck.state);
      }

      // 6. 仓位管理
      if (config.enableRiskManagement) {
        await this.positionController.checkAndExecutePositionAdjustment(symbol, sessionId);
      }

      this.logger.debug(`交易周期完成: ${symbol} - 信号: ${gridSignal.type}`);

    } catch (error) {
      this.logger.error(`交易周期执行失败 ${symbol}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 执行网格信号
   */
  private async executeGridSignal(
    sessionId: string,
    config: GridTradingConfig,
    signal: any,
    riskState: RiskState,
  ): Promise<void> {
    const symbol = config.symbol;

    // 检查风险状态是否允许该操作
    if (
      (signal.type === 'buy' && riskState === RiskState.ALLOW_SELL_ONLY) ||
      (signal.type === 'sell' && riskState === RiskState.ALLOW_BUY_ONLY)
    ) {
      this.logger.warn(`风险状态不允许 ${signal.type} 操作: ${symbol}`);
      return;
    }

    try {
      // 余额检查
      const hasBalanceRisk = await this.riskManager.checkBalanceRisk(
        symbol,
        signal.quantity * signal.price,
        signal.type === 'buy' ? symbol.split('/')[1] : symbol.split('/')[0],
      );

      if (hasBalanceRisk) {
        this.logger.warn(`余额不足，无法执行 ${signal.type} 操作: ${symbol}`);
        return;
      }

      // 执行订单
      const orderRequest = {
        symbol,
        sessionId,
        side: signal.type,
        price: signal.price,
        quantity: signal.quantity,
        gridLevel: signal.gridLevel,
      };

      const result = await this.orderManager.executeOrder(orderRequest);

      if (result.success) {
        this.logger.log(
          `网格信号执行成功: ${symbol} ${signal.type} ${signal.quantity} @ ${signal.price} - ${signal.reason}`,
        );
        
        // 重置失败计数
        this.riskManager.resetFailureCount(symbol);
      } else {
        this.logger.error(`网格信号执行失败: ${result.error}`);
        
        // 记录失败
        await this.riskManager.recordTradingFailure(
          symbol,
          sessionId,
          result.error || '未知错误',
        );
      }

    } catch (error) {
      this.logger.error(`执行网格信号失败: ${error.message}`, error.stack);
      
      await this.riskManager.recordTradingFailure(
        symbol,
        sessionId,
        error.message,
      );
    }
  }

  /**
   * 停止交易会话
   */
  async stopTradingSession(sessionId: string): Promise<void> {
    const session = this.activeSessions.get(sessionId);
    if (session) {
      session.isRunning = false;
      this.logger.log(`停止交易会话: ${sessionId}`);
    }
  }

  /**
   * 获取活跃会话状态
   */
  getActiveSessionsStatus(): Array<{
    sessionId: string;
    isRunning: boolean;
    lastActivity: Date;
    consecutiveErrors: number;
  }> {
    return Array.from(this.activeSessions.entries()).map(([sessionId, session]) => ({
      sessionId,
      ...session,
    }));
  }

  /**
   * 睡眠函数
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
