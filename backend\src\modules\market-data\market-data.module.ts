import { Module } from '@nestjs/common';
import { BinanceApiService } from '../../common/services/binance-api.service';
import { MarketDataService } from '../../common/services/market-data.service';
import { TechnicalAnalysisService } from '../../common/services/technical-analysis.service';
import { StrategyService } from '../../common/services/strategy.service';

@Module({
  providers: [
    BinanceApiService,
    MarketDataService,
    TechnicalAnalysisService,
    StrategyService,
  ],
  exports: [
    BinanceApiService,
    MarketDataService,
    TechnicalAnalysisService,
    StrategyService,
  ],
})
export class MarketDataModule {}
