import { Injectable } from '@nestjs/common';
import { Kline, TechnicalIndicators } from '@signal-dash/shared';
import { INDICATOR_PARAMS } from '@signal-dash/shared';
import {
  EMAIndicator,
  RSIIndicator,
  ATRIndicator,
  BollingerBandsIndicator,
} from '../indicators';

@Injectable()
export class TechnicalAnalysisService {
  /**
   * 计算所有技术指标
   */
  calculateIndicators(klines: Kline[]): TechnicalIndicators | null {
    if (klines.length < 200) {
      // 需要足够的数据来计算EMA200
      return null;
    }
    
    try {
      // 创建指标实例
      const ema20 = new EMAIndicator(INDICATOR_PARAMS.EMA.SHORT);
      const ema50 = new EMAIndicator(INDICATOR_PARAMS.EMA.MEDIUM);
      const ema200 = new EMAIndicator(INDICATOR_PARAMS.EMA.LONG);
      const rsi = new RSIIndicator(INDICATOR_PARAMS.RSI.PERIOD);
      const atr = new ATRIndicator(INDICATOR_PARAMS.ATR.PERIOD);
      const bb = new BollingerBandsIndicator(
        INDICATOR_PARAMS.BOLLINGER_BANDS.PERIOD,
        INDICATOR_PARAMS.BOLLINGER_BANDS.MULTIPLIER
      );
      
      // 更新数据
      ema20.update(klines);
      ema50.update(klines);
      ema200.update(klines);
      rsi.update(klines);
      atr.update(klines);
      bb.update(klines);
      
      // 获取最新值
      const ema20Value = ema20.getLatest();
      const ema50Value = ema50.getLatest();
      const ema200Value = ema200.getLatest();
      const rsiValue = rsi.getLatest();
      const atrValue = atr.getLatest();
      const bbValue = bb.getLatest();
      
      if (!ema20Value || !ema50Value || !ema200Value || !rsiValue || !atrValue || !bbValue) {
        return null;
      }
      
      return {
        ema20: ema20Value,
        ema50: ema50Value,
        ema200: ema200Value,
        rsi: rsiValue,
        atr: atrValue,
        bollingerBands: {
          upper: bbValue.upper,
          middle: bbValue.middle,
          lower: bbValue.lower,
          bandwidth: bbValue.bandwidth,
        },
      };
    } catch (error) {
      console.error('计算技术指标时出错:', error);
      return null;
    }
  }
  
  /**
   * 判断趋势方向
   */
  getTrendDirection(indicators: TechnicalIndicators): 'BULLISH' | 'BEARISH' | 'SIDEWAYS' {
    const { ema50, ema200 } = indicators;
    
    if (ema50 > ema200 * 1.01) {
      return 'BULLISH';
    } else if (ema50 < ema200 * 0.99) {
      return 'BEARISH';
    } else {
      return 'SIDEWAYS';
    }
  }
  
  /**
   * 检测吞没K线形态
   */
  detectEngulfingPattern(klines: Kline[]): {
    isBullishEngulfing: boolean;
    isBearishEngulfing: boolean;
  } {
    if (klines.length < 2) {
      return { isBullishEngulfing: false, isBearishEngulfing: false };
    }
    
    const prev = klines[klines.length - 2];
    const current = klines[klines.length - 1];
    
    const prevBody = Math.abs(prev.close - prev.open);
    const currentBody = Math.abs(current.close - current.open);
    
    // 看涨吞没：前一根为阴线，当前为阳线，且当前K线完全包含前一根
    const isBullishEngulfing = 
      prev.close < prev.open && // 前一根为阴线
      current.close > current.open && // 当前为阳线
      current.open < prev.close && // 当前开盘价低于前一根收盘价
      current.close > prev.open && // 当前收盘价高于前一根开盘价
      currentBody > prevBody * 1.1; // 当前实体比前一根大10%以上
    
    // 看跌吞没：前一根为阳线，当前为阴线，且当前K线完全包含前一根
    const isBearishEngulfing = 
      prev.close > prev.open && // 前一根为阳线
      current.close < current.open && // 当前为阴线
      current.open > prev.close && // 当前开盘价高于前一根收盘价
      current.close < prev.open && // 当前收盘价低于前一根开盘价
      currentBody > prevBody * 1.1; // 当前实体比前一根大10%以上
    
    return { isBullishEngulfing, isBearishEngulfing };
  }
  
  /**
   * 检查价格是否触及EMA
   */
  isPriceTouchingEMA(price: number, emaValue: number, tolerance: number = 0.005): boolean {
    return Math.abs(price - emaValue) / emaValue <= tolerance;
  }
}
