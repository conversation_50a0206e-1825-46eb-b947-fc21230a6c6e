import { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { SystemConfig, TradingPair } from '@signal-dash/shared';
import { api } from '../services/api';
import { Loader2, CheckCircle, XCircle, AlertCircle } from 'lucide-react';

export function Configuration() {
  const queryClient = useQueryClient();
  const [config, setConfig] = useState<SystemConfig | null>(null);
  const [hasChanges, setHasChanges] = useState(false);

  // 获取当前配置
  const { data: currentConfig, isLoading: configLoading } = useQuery({
    queryKey: ['system-config'],
    queryFn: api.config.get,
  });

  // 获取系统状态
  const { data: systemStatus, isLoading: statusLoading } = useQuery({
    queryKey: ['system-status'],
    queryFn: api.config.getSystemStatus,
    refetchInterval: 30000,
  });

  // 更新配置
  const updateConfigMutation = useMutation({
    mutationFn: api.config.update,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['system-config'] });
      setHasChanges(false);
      alert('配置保存成功');
    },
    onError: (error) => {
      console.error('保存配置失败:', error);
      alert('保存配置失败');
    },
  });

  // 测试通知
  const testNotificationMutation = useMutation({
    mutationFn: api.config.testNotification,
    onSuccess: (data) => {
      alert(data.message);
    },
    onError: (error) => {
      console.error('测试通知失败:', error);
      alert('测试通知失败');
    },
  });

  // 初始化配置
  useEffect(() => {
    if (currentConfig) {
      setConfig(currentConfig);
    }
  }, [currentConfig]);

  const handleConfigChange = (section: keyof SystemConfig, field: string, value: any) => {
    if (!config) return;

    setConfig(prev => ({
      ...prev!,
      [section]: {
        ...prev![section],
        [field]: value,
      },
    }));
    setHasChanges(true);
  };

  const handleAssetToggle = (asset: TradingPair, checked: boolean) => {
    if (!config) return;

    const currentAssets = config.monitoring.assets;
    const newAssets = checked
      ? [...currentAssets, asset]
      : currentAssets.filter(a => a !== asset);

    handleConfigChange('monitoring', 'assets', newAssets);
  };

  const handleSaveConfig = () => {
    if (!config) return;
    updateConfigMutation.mutate(config);
  };

  const handleTestNotification = () => {
    testNotificationMutation.mutate();
  };

  const getStatusIcon = (status: boolean) => {
    return status ? (
      <CheckCircle className="h-5 w-5 text-green-600" />
    ) : (
      <XCircle className="h-5 w-5 text-red-600" />
    );
  };

  const getStatusText = (status: boolean) => {
    return status ? '正常' : '异常';
  };

  const getStatusColor = (status: boolean) => {
    return status ? 'text-green-600' : 'text-red-600';
  };

  if (configLoading || !config) {
    return (
      <div className="flex items-center justify-center py-12">
        <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
        <span className="ml-2 text-gray-600">加载配置中...</span>
      </div>
    );
  }

  return (
    <div>
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">系统配置</h1>
        <p className="mt-2 text-gray-600">
          管理系统设置和监控任务
        </p>
      </div>

      <div className="space-y-6">
        {/* 系统状态 */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">系统状态</h3>

          {statusLoading ? (
            <div className="flex items-center">
              <Loader2 className="h-5 w-5 animate-spin text-blue-600 mr-2" />
              <span className="text-gray-600">检查中...</span>
            </div>
          ) : (
            <div className="grid grid-cols-2 gap-4">
              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <span className="text-sm font-medium text-gray-700">数据库</span>
                <div className="flex items-center">
                  {getStatusIcon(systemStatus?.database || false)}
                  <span className={`ml-2 text-sm ${getStatusColor(systemStatus?.database || false)}`}>
                    {getStatusText(systemStatus?.database || false)}
                  </span>
                </div>
              </div>

              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <span className="text-sm font-medium text-gray-700">币安API</span>
                <div className="flex items-center">
                  {getStatusIcon(systemStatus?.binanceApi || false)}
                  <span className={`ml-2 text-sm ${getStatusColor(systemStatus?.binanceApi || false)}`}>
                    {getStatusText(systemStatus?.binanceApi || false)}
                  </span>
                </div>
              </div>

              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <span className="text-sm font-medium text-gray-700">Redis</span>
                <div className="flex items-center">
                  {getStatusIcon(systemStatus?.redis || false)}
                  <span className={`ml-2 text-sm ${getStatusColor(systemStatus?.redis || false)}`}>
                    {getStatusText(systemStatus?.redis || false)}
                  </span>
                </div>
              </div>

              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <span className="text-sm font-medium text-gray-700">通知服务</span>
                <div className="flex items-center">
                  {getStatusIcon(systemStatus?.notification || false)}
                  <span className={`ml-2 text-sm ${getStatusColor(systemStatus?.notification || false)}`}>
                    {getStatusText(systemStatus?.notification || false)}
                  </span>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* ntfy配置 */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">推送通知配置</h3>

          <div className="space-y-4">
            <div>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={config.ntfy.enabled}
                  onChange={(e) => handleConfigChange('ntfy', 'enabled', e.target.checked)}
                  className="mr-2"
                />
                启用推送通知
              </label>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                ntfy服务器地址
              </label>
              <input
                type="url"
                value={config.ntfy.url}
                onChange={(e) => handleConfigChange('ntfy', 'url', e.target.value)}
                className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                推送主题
              </label>
              <input
                type="text"
                value={config.ntfy.topic}
                onChange={(e) => handleConfigChange('ntfy', 'topic', e.target.value)}
                placeholder="your-topic-name"
                className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              />
            </div>

            <div>
              <button
                onClick={handleTestNotification}
                disabled={testNotificationMutation.isPending || !config.ntfy.enabled}
                className="bg-gray-600 text-white py-2 px-4 rounded-md hover:bg-gray-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
              >
                {testNotificationMutation.isPending ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    测试中...
                  </>
                ) : (
                  '测试通知'
                )}
              </button>
            </div>
          </div>
        </div>

        {/* 监控配置 */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">自动监控配置</h3>

          <div className="space-y-4">
            <div>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={config.monitoring.enabled}
                  onChange={(e) => handleConfigChange('monitoring', 'enabled', e.target.checked)}
                  className="mr-2"
                />
                启用自动监控
              </label>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                监控间隔 (分钟)
              </label>
              <input
                type="number"
                value={config.monitoring.interval}
                onChange={(e) => handleConfigChange('monitoring', 'interval', Number(e.target.value))}
                min={1}
                max={60}
                className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                监控交易对
              </label>
              <div className="space-y-2">
                {(['BTC/USDT', 'ETH/USDT', 'SOL/USDT'] as TradingPair[]).map((asset) => (
                  <label key={asset} className="flex items-center">
                    <input
                      type="checkbox"
                      checked={config.monitoring.assets.includes(asset)}
                      onChange={(e) => handleAssetToggle(asset, e.target.checked)}
                      className="mr-2"
                    />
                    {asset}
                  </label>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* 保存按钮 */}
        <div className="flex items-center justify-between">
          {hasChanges && (
            <div className="flex items-center text-amber-600">
              <AlertCircle className="h-5 w-5 mr-2" />
              <span className="text-sm">有未保存的更改</span>
            </div>
          )}

          <button
            onClick={handleSaveConfig}
            disabled={updateConfigMutation.isPending || !hasChanges}
            className="bg-blue-600 text-white py-2 px-6 rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center ml-auto"
          >
            {updateConfigMutation.isPending ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                保存中...
              </>
            ) : (
              '保存配置'
            )}
          </button>
        </div>
      </div>
    </div>
  );
}
