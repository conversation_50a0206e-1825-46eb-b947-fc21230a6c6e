import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';

export enum PositionSide {
  LONG = 'long',
  SHORT = 'short',
}

export enum PositionStatus {
  OPEN = 'open',
  CLOSED = 'closed',
  PARTIAL = 'partial',
}

@Entity('trading_positions')
@Index(['symbol', 'createdAt'])
@Index(['symbol', 'status'])
export class TradingPosition {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 20 })
  symbol: string;

  @Column({ type: 'varchar', length: 100 })
  sessionId: string;

  @Column({
    type: 'enum',
    enum: PositionSide,
  })
  side: PositionSide;

  @Column({
    type: 'enum',
    enum: PositionStatus,
    default: PositionStatus.OPEN,
  })
  status: PositionStatus;

  @Column({ type: 'decimal', precision: 18, scale: 8 })
  entryPrice: number;

  @Column({ type: 'decimal', precision: 18, scale: 8, nullable: true })
  exitPrice: number;

  @Column({ type: 'decimal', precision: 18, scale: 8 })
  quantity: number;

  @Column({ type: 'decimal', precision: 18, scale: 8 })
  remainingQuantity: number;

  @Column({ type: 'decimal', precision: 18, scale: 8 })
  totalValue: number;

  @Column({ type: 'decimal', precision: 18, scale: 8, default: 0 })
  realizedPnl: number;

  @Column({ type: 'decimal', precision: 18, scale: 8, default: 0 })
  unrealizedPnl: number;

  @Column({ type: 'decimal', precision: 18, scale: 8, default: 0 })
  fees: number;

  @Column({ type: 'decimal', precision: 10, scale: 4 })
  gridLevel: number;

  @Column({ type: 'varchar', length: 100, nullable: true })
  entryOrderId: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  exitOrderId: string;

  @Column({ type: 'timestamp', nullable: true })
  closedAt: Date;

  @Column({ type: 'json', nullable: true })
  metadata: Record<string, any>;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
