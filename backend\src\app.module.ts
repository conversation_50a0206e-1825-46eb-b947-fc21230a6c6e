import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BullModule } from '@nestjs/bull';
import { ScheduleModule } from '@nestjs/schedule';

import { AppController } from './app.controller';
import { AppService } from './app.service';

// 配置
import { databaseConfig } from './config/database.config';
import { redisConfig } from './config/redis.config';

// 模块
import { AnalysisModule } from './modules/analysis/analysis.module';
import { BacktestModule } from './modules/backtest/backtest.module';
import { SignalModule as FullSignalModule } from './modules/signal/signal.module';
import { MarketDataModule } from './modules/market-data/market-data.module';
import { ConfigurationModule } from './modules/configuration/configuration.module';
import { WebsocketModule } from './modules/websocket/websocket.module';
import { GridTradingModule } from './modules/grid-trading/grid-trading.module';
import { GridTradingMemoryModule } from './modules/grid-trading-memory/grid-trading-memory.module';
import { SignalsMemoryModule } from './modules/signals-memory/signals-memory.module';
import { AnalysisMemoryModule } from './modules/analysis-memory/analysis-memory.module';
import { BacktestMemoryModule } from './modules/backtest-memory/backtest-memory.module';

// 简化模块（用于演示）
import { SignalsModule } from './modules/signals/signals.module';
import { ConfigModule as SimpleConfigModule } from './modules/config/config.module';
import { BacktestSimpleModule } from './modules/backtest-simple/backtest.module';
import { MonitoringModule } from './modules/monitoring/monitoring.module';

@Module({
  imports: [
    // 配置模块
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
    }),
    
    // 数据库模块 - 暂时禁用
    // TypeOrmModule.forRootAsync(databaseConfig),
    
    // Redis和任务队列模块
    BullModule.forRoot({
      redis: {
        host: process.env.REDIS_HOST || 'localhost',
        port: parseInt(process.env.REDIS_PORT || '6379'),
      },
    }),
    
    // 定时任务模块
    ScheduleModule.forRoot(),
    
    // 业务模块 - 暂时禁用需要数据库的模块
    // AnalysisModule,
    // BacktestModule,
    // FullSignalModule,
    MarketDataModule,
    // ConfigurationModule,
    WebsocketModule,
    // GridTradingModule,
    GridTradingMemoryModule,
    SignalsMemoryModule,
    AnalysisMemoryModule,
    BacktestMemoryModule,

    // 简化模块（用于演示）
    // SignalsModule,
    SimpleConfigModule,
    // BacktestSimpleModule,
    // MonitoringModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
