import { useEffect, useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { TradingSignal, SignalType, StrategyType } from '@signal-dash/shared';
import { api } from '../services/api';
import { websocketService } from '../services/websocket';
import { formatDistanceToNow } from 'date-fns';
import { zhCN } from 'date-fns/locale';

interface MonitoringResult {
  id: string;
  asset: string;
  strategy: StrategyType;
  signal: {
    signalType: SignalType;
    score: number;
    price: number;
    stopLoss?: number;
    takeProfit?: number;
  } | null;
  isQualified: boolean;
  reason?: string;
  timestamp: string;
}

export function Dashboard() {
  const [realtimeSignals, setRealtimeSignals] = useState<TradingSignal[]>([]);
  const [monitoringResults, setMonitoringResults] = useState<MonitoringResult[]>([]);
  const [lastMonitoringTime, setLastMonitoringTime] = useState<Date | null>(null);

  // 获取信号统计
  const { data: stats, isLoading: statsLoading } = useQuery({
    queryKey: ['signal-stats'],
    queryFn: api.signals.getStats,
    refetchInterval: 30000, // 30秒刷新一次
  });

  // 获取最近信号
  const { data: signalsData, isLoading: signalsLoading } = useQuery({
    queryKey: ['recent-signals'],
    queryFn: () => api.signals.getList({ limit: 10 }),
    refetchInterval: 60000, // 1分钟刷新一次
  });

  // 获取系统状态
  const { data: systemStatus, isLoading: statusLoading } = useQuery({
    queryKey: ['system-status'],
    queryFn: api.config.getSystemStatus,
    refetchInterval: 30000,
  });

  // WebSocket连接和信号订阅
  useEffect(() => {
    const connectWebSocket = async () => {
      try {
        await websocketService.connect();

        // 订阅信号更新
        websocketService.subscribeToSignals((signal) => {
          setRealtimeSignals(prev => [signal, ...prev.slice(0, 9)]);
        });

        // 订阅监控结果
        websocketService.socket?.on('monitoring:results', (data: { timestamp: string; results: MonitoringResult[] }) => {
          console.log('收到监控结果:', data);
          setMonitoringResults(data.results);
          setLastMonitoringTime(new Date(data.timestamp));
        });

      } catch (error) {
        console.error('WebSocket连接失败:', error);
      }
    };

    connectWebSocket();

    return () => {
      websocketService.unsubscribeFromSignals();
      websocketService.socket?.off('monitoring:results');
      websocketService.disconnect();
    };
  }, []);

  const getSignalTypeColor = (signalType: SignalType) => {
    switch (signalType) {
      case SignalType.BUY:
        return 'text-green-600 bg-green-100';
      case SignalType.SELL:
        return 'text-red-600 bg-red-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getSignalTypeText = (signalType: SignalType) => {
    switch (signalType) {
      case SignalType.BUY:
        return '买入';
      case SignalType.SELL:
        return '卖出';
      default:
        return '观望';
    }
  };

  const getSystemStatusColor = (status: boolean) => {
    return status ? 'text-green-600' : 'text-red-600';
  };

  const getStrategyText = (strategy: StrategyType) => {
    return strategy === StrategyType.TREND_FOLLOWING ? '趋势跟踪' : '震荡区间';
  };

  const getQualificationColor = (isQualified: boolean) => {
    return isQualified ? 'text-green-600 bg-green-50' : 'text-gray-600 bg-gray-50';
  };

  const allSignals = [...realtimeSignals, ...(signalsData?.signals || [])];
  const uniqueSignals = allSignals.filter((signal, index, self) =>
    index === self.findIndex(s => s.id === signal.id)
  ).slice(0, 10);

  return (
    <div>
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">仪表盘</h1>
        <p className="mt-2 text-gray-600">
          实时监控交易信号和系统状态
        </p>
      </div>

      <div className="grid grid-cols-1 gap-6 lg:grid-cols-4">
        {/* 统计卡片 */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900">今日信号</h3>
          <p className="mt-2 text-3xl font-bold text-blue-600">
            {statsLoading ? '-' : stats?.todaySignals || 0}
          </p>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900">总信号数</h3>
          <p className="mt-2 text-3xl font-bold text-purple-600">
            {statsLoading ? '-' : stats?.totalSignals || 0}
          </p>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900">活跃监控</h3>
          <p className="mt-2 text-3xl font-bold text-green-600">3</p>
          <p className="text-sm text-gray-500">BTC, ETH, SOL</p>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900">系统状态</h3>
          <div className="mt-2">
            {statusLoading ? (
              <p className="text-gray-500">检查中...</p>
            ) : (
              <div className="space-y-1">
                <div className="flex items-center justify-between">
                  <span className="text-sm">数据库</span>
                  <span className={`text-sm ${getSystemStatusColor(systemStatus?.database || false)}`}>
                    {systemStatus?.database ? '正常' : '异常'}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">API</span>
                  <span className={`text-sm ${getSystemStatusColor(systemStatus?.binanceApi || false)}`}>
                    {systemStatus?.binanceApi ? '正常' : '异常'}
                  </span>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 实时监控结果 */}
      <div className="mt-8">
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
            <h3 className="text-lg font-medium text-gray-900">实时监控</h3>
            <div className="text-sm text-gray-500">
              {lastMonitoringTime ? (
                <>最后更新: {formatDistanceToNow(lastMonitoringTime, { addSuffix: true, locale: zhCN })}</>
              ) : (
                '等待监控数据...'
              )}
            </div>
          </div>
          <div className="p-6">
            {monitoringResults.length === 0 ? (
              <div className="text-center text-gray-500 py-8">
                等待监控结果...
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {monitoringResults.map((result) => (
                  <div key={result.id} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <span className="font-medium text-gray-900">{result.asset}</span>
                      <span className="text-sm text-gray-500">{getStrategyText(result.strategy)}</span>
                    </div>

                    {result.signal ? (
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getSignalTypeColor(result.signal.signalType)}`}>
                            {getSignalTypeText(result.signal.signalType)}
                          </span>
                          <span className="text-sm font-medium">评分: {result.signal.score}</span>
                        </div>
                        <div className="text-sm text-gray-600">
                          价格: ${result.signal.price.toFixed(2)}
                        </div>
                        {result.signal.stopLoss && (
                          <div className="text-sm text-red-600">
                            止损: ${result.signal.stopLoss.toFixed(2)}
                          </div>
                        )}
                      </div>
                    ) : (
                      <div className="text-sm text-gray-500">无信号</div>
                    )}

                    <div className="mt-3 pt-3 border-t border-gray-100">
                      <div className={`text-xs px-2 py-1 rounded ${getQualificationColor(result.isQualified)}`}>
                        {result.isQualified ? '✓ 符合记录要求' : `✗ ${result.reason}`}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 最近信号 */}
      <div className="mt-8">
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">最近信号</h3>
          </div>
          <div className="p-6">
            {signalsLoading ? (
              <div className="text-center text-gray-500 py-8">
                加载中...
              </div>
            ) : uniqueSignals.length === 0 ? (
              <div className="text-center text-gray-500 py-8">
                暂无信号数据
              </div>
            ) : (
              <div className="space-y-4">
                {uniqueSignals.map((signal) => (
                  <div key={signal.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                    <div className="flex items-center space-x-4">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getSignalTypeColor(signal.signalType)}`}>
                        {getSignalTypeText(signal.signalType)}
                      </span>
                      <div>
                        <p className="font-medium text-gray-900">{signal.asset}</p>
                        <p className="text-sm text-gray-500">
                          {signal.strategy === 'TREND_FOLLOWING' ? '趋势跟踪' : '震荡区间'}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-medium text-gray-900">评分: {signal.score}</p>
                      <p className="text-sm text-gray-500">
                        {formatDistanceToNow(new Date(signal.timestamp), {
                          addSuffix: true,
                          locale: zhCN
                        })}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
