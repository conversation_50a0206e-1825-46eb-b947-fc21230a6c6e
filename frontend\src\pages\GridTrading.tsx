import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  Play,
  Square,
  Settings,
  RefreshCw
} from 'lucide-react';
import { gridTradingApi } from '../services/gridTradingApi';
import { GridTradingConfig } from '../types/gridTrading';
import { GridConfigModal } from '../components/GridConfigModal';
import { GridStatusCard } from '../components/GridStatusCard';
import { GridPositionsTable } from '../components/GridPositionsTable';
import { GridPerformanceChart } from '../components/GridPerformanceChart';

export function GridTrading() {
  const [selectedSymbol, setSelectedSymbol] = useState<string>('BTC/USDT');
  const [showConfigModal, setShowConfigModal] = useState(false);
  const [editingConfig, setEditingConfig] = useState<GridTradingConfig | null>(null);
  const queryClient = useQueryClient();

  // 获取网格交易配置列表
  const { data: configs } = useQuery({
    queryKey: ['grid-configs'],
    queryFn: gridTradingApi.getConfigs,
    refetchInterval: 30000, // 30秒刷新
  });

  // 获取活跃会话状态
  const { data: activeSessions, isLoading: sessionsLoading } = useQuery({
    queryKey: ['grid-sessions'],
    queryFn: gridTradingApi.getActiveSessions,
    refetchInterval: 5000, // 5秒刷新
  });

  // 获取选中交易对的详细状态
  const { data: gridStatus } = useQuery({
    queryKey: ['grid-status', selectedSymbol],
    queryFn: () => gridTradingApi.getStatus(selectedSymbol),
    enabled: !!selectedSymbol,
    refetchInterval: 10000, // 10秒刷新
  });

  // 获取仓位信息
  const { data: positions } = useQuery({
    queryKey: ['grid-positions', selectedSymbol],
    queryFn: () => gridTradingApi.getPositions(selectedSymbol),
    enabled: !!selectedSymbol,
    refetchInterval: 15000, // 15秒刷新
  });

  // 获取交易表现
  const { data: performance } = useQuery({
    queryKey: ['grid-performance', selectedSymbol],
    queryFn: () => gridTradingApi.getPerformance(selectedSymbol),
    enabled: !!selectedSymbol,
    refetchInterval: 30000, // 30秒刷新
  });

  // 启动网格交易
  const startGridMutation = useMutation({
    mutationFn: gridTradingApi.startGrid,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['grid-sessions'] });
      queryClient.invalidateQueries({ queryKey: ['grid-status'] });
    },
  });

  // 停止网格交易
  const stopGridMutation = useMutation({
    mutationFn: gridTradingApi.stopGrid,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['grid-sessions'] });
      queryClient.invalidateQueries({ queryKey: ['grid-status'] });
    },
  });

  // 创建或更新配置
  const saveConfigMutation = useMutation({
    mutationFn: (config: Partial<GridTradingConfig>) => 
      editingConfig 
        ? gridTradingApi.updateConfig(editingConfig.id, config)
        : gridTradingApi.createConfig(config as GridTradingConfig),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['grid-configs'] });
      setShowConfigModal(false);
      setEditingConfig(null);
    },
  });

  const currentConfig = configs?.find(c => c.symbol === selectedSymbol);
  const isRunning = activeSessions?.some(s => s.symbol === selectedSymbol && s.isActive);

  const handleStartGrid = () => {
    if (!currentConfig) {
      setShowConfigModal(true);
      return;
    }
    
    startGridMutation.mutate({
      symbol: selectedSymbol,
      initialCapital: currentConfig.initialCapital,
      gridSize: currentConfig.gridSize,
      enableVolatilityAdjustment: currentConfig.enableVolatilityAdjustment,
      enableRiskManagement: currentConfig.enableRiskManagement,
    });
  };

  const handleStopGrid = () => {
    stopGridMutation.mutate(selectedSymbol);
  };

  const handleEditConfig = () => {
    setEditingConfig(currentConfig || null);
    setShowConfigModal(true);
  };

  const handleCreateConfig = () => {
    setEditingConfig(null);
    setShowConfigModal(true);
  };

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">网格交易</h1>
          <p className="text-gray-600">智能网格交易系统，基于波动率动态调整</p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={handleCreateConfig}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
          >
            <Settings className="h-4 w-4 mr-2" />
            新建配置
          </button>
          <button
            onClick={() => queryClient.invalidateQueries()}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            刷新
          </button>
        </div>
      </div>

      {/* 交易对选择和控制面板 */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-4">
            <label className="text-sm font-medium text-gray-700">交易对:</label>
            <select
              value={selectedSymbol}
              onChange={(e) => setSelectedSymbol(e.target.value)}
              className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="BTC/USDT">BTC/USDT</option>
              <option value="ETH/USDT">ETH/USDT</option>
              <option value="BNB/USDT">BNB/USDT</option>
              <option value="ADA/USDT">ADA/USDT</option>
              <option value="SOL/USDT">SOL/USDT</option>
            </select>
          </div>

          <div className="flex items-center space-x-3">
            {currentConfig && (
              <button
                onClick={handleEditConfig}
                className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                <Settings className="h-4 w-4 mr-1" />
                配置
              </button>
            )}
            
            {isRunning ? (
              <button
                onClick={handleStopGrid}
                disabled={stopGridMutation.isPending}
                className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 disabled:opacity-50"
              >
                <Square className="h-4 w-4 mr-2" />
                {stopGridMutation.isPending ? '停止中...' : '停止交易'}
              </button>
            ) : (
              <button
                onClick={handleStartGrid}
                disabled={startGridMutation.isPending}
                className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 disabled:opacity-50"
              >
                <Play className="h-4 w-4 mr-2" />
                {startGridMutation.isPending ? '启动中...' : '开始交易'}
              </button>
            )}
          </div>
        </div>

        {/* 状态指示器 */}
        <div className="flex items-center space-x-6 text-sm">
          <div className="flex items-center">
            <div className={`w-3 h-3 rounded-full mr-2 ${isRunning ? 'bg-green-500' : 'bg-gray-400'}`} />
            <span className="text-gray-600">
              状态: {isRunning ? '运行中' : '已停止'}
            </span>
          </div>
          
          {currentConfig && (
            <>
              <div className="text-gray-600">
                网格大小: {currentConfig.gridSize}%
              </div>
              <div className="text-gray-600">
                初始资金: ${currentConfig.initialCapital.toLocaleString()}
              </div>
            </>
          )}
        </div>
      </div>

      {/* 状态卡片 */}
      {gridStatus && (
        <GridStatusCard 
          status={gridStatus} 
          isLoading={sessionsLoading}
        />
      )}

      {/* 表现图表 */}
      {performance && (
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">交易表现</h3>
          <GridPerformanceChart data={performance} />
        </div>
      )}

      {/* 仓位表格 */}
      {positions && (
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">当前仓位</h3>
          </div>
          <GridPositionsTable positions={positions} />
        </div>
      )}

      {/* 配置模态框 */}
      {showConfigModal && (
        <GridConfigModal
          config={editingConfig}
          symbol={selectedSymbol}
          onSave={(config) => saveConfigMutation.mutate(config)}
          onClose={() => {
            setShowConfigModal(false);
            setEditingConfig(null);
          }}
          isLoading={saveConfigMutation.isPending}
        />
      )}
    </div>
  );
}
