import { Controller, Get, Post, Body } from '@nestjs/common';
import { SignalsMemoryService } from './signals-memory.service';

@Controller()
export class SignalsMemoryController {
  constructor(private readonly signalsService: SignalsMemoryService) {}

  @Get('signals/stats/summary')
  async getSignalsSummary() {
    return await this.signalsService.getSignalsSummary();
  }

  @Post('analyze-now')
  async analyzeNow(@Body() request: { asset: string; strategy: string }) {
    return await this.signalsService.analyzeNow(request);
  }
}
