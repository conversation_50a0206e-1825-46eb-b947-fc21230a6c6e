import { IsOptional, IsBoolean, IsString, IsNumber, IsArray, Min, <PERSON>, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { TradingPair } from '@signal-dash/shared';

class NtfyConfigDto {
  @IsOptional()
  @IsString()
  url?: string;

  @IsOptional()
  @IsString()
  topic?: string;

  @IsOptional()
  @IsBoolean()
  enabled?: boolean;
}

class BinanceConfigDto {
  @IsOptional()
  @IsString()
  apiUrl?: string;

  @IsOptional()
  @IsBoolean()
  testnet?: boolean;
}

class MonitoringConfigDto {
  @IsOptional()
  @IsBoolean()
  enabled?: boolean;

  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(60)
  @Type(() => Number)
  interval?: number;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  assets?: TradingPair[];
}

export class UpdateConfigDto {
  @IsOptional()
  @ValidateNested()
  @Type(() => NtfyConfigDto)
  ntfy?: NtfyConfigDto;

  @IsOptional()
  @ValidateNested()
  @Type(() => BinanceConfigDto)
  binance?: BinanceConfigDto;

  @IsOptional()
  @ValidateNested()
  @Type(() => MonitoringConfigDto)
  monitoring?: MonitoringConfigDto;
}
